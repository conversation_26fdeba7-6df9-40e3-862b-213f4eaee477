package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.system.domain.imagescanrecord.ImageScanRecordResponse;
import com.youying.system.service.ImageScanRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 纸质票扫描
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@RestController
@RequestMapping("/imageScanRecord")
public class ImageScanRecordController extends BaseController {

    @Autowired
    private ImageScanRecordService imageScanRecordService;

    /**
     * 查询列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<ImageScanRecordResponse>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(imageScanRecordService.list(pageDomain)));
    }

    /**
     * 查询详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<ImageScanRecordResponse> details(Long id) {
        return R.ok(imageScanRecordService.details(id));
    }

}
