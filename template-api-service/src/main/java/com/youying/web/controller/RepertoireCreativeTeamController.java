package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RepertoireCreativeTeam;
import com.youying.system.service.RepertoireCreativeTeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 剧目主创团队表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/repertoireCreativeTeam")
public class RepertoireCreativeTeamController extends BaseController {

    @Autowired
    private RepertoireCreativeTeamService repertoireCreativeTeamService;

    /**
     * 根据剧目ID查询主创团队表列表
     *
     * @return
     */
    @GetMapping(value = "/listByRepertoireId")
    public R<List<RepertoireCreativeTeam>> listByRepertoireId(Long repertoireId) {
        return R.ok(repertoireCreativeTeamService.list(new LambdaQueryWrapper<RepertoireCreativeTeam>()
                .eq(RepertoireCreativeTeam::getRepertoireId, repertoireId)));
    }

    /**
     * 查询剧目主创团队表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<RepertoireCreativeTeam> details(Long id) {
        return R.ok(repertoireCreativeTeamService.getById(id));
    }

}

