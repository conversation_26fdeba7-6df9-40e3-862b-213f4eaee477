package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserLook;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.UserLookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 用户观影表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/userLook")
public class UserLookController extends BaseController {

    @Autowired
    private UserLookService userLookService;

    /**
     * 查询用户观影表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserLook>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(userLookService.list()));
    }


    /**
     * 查询用户观影表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<UserLook>> list() {
        return R.ok(userLookService.list());
    }

    /**
     * 查询用户观影表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserLook> details(Long id) {
        return R.ok(userLookService.getById(id));
    }

    /**
     * 添加用户观影表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加用户观影表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody UserLook userLook) {
        return R.ok(userLookService.save(userLook));
    }

    /**
     * 修改用户观影表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改用户观影表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody UserLook userLook) {
        return R.ok(userLookService.updateById(userLook));
    }

    /**
     * 删除用户观影表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除用户观影表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(userLookService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

