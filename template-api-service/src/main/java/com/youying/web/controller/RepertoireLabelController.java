package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RepertoireLabel;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.domain.repertoirelabel.RepertoireLabelRequest;
import com.youying.system.domain.repertoirelabel.RepertoireLabelResponse;
import com.youying.system.service.RepertoireLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * 剧目标签表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/repertoireLabel")
public class RepertoireLabelController extends BaseController {

    @Autowired
    private RepertoireLabelService repertoireLabelService;

    /**
     * 查询剧目标签表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<RepertoireLabelResponse>> listByPage(@RequestBody RepertoireLabelRequest request) {
        startPage(request);
        return R.ok(getTableList(repertoireLabelService.listByPage(request)));
    }

    /**
     * 查询剧目标签表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<RepertoireLabel> details(Long id) {
        return R.ok(repertoireLabelService.getById(id));
    }

    /**
     * 修改剧目标签表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改剧目标签表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody RepertoireLabel repertoireLabel) {
        return R.ok(repertoireLabelService.updateById(repertoireLabel));
    }

    /**
     * 删除剧目标签表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除剧目标签表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(repertoireLabelService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

