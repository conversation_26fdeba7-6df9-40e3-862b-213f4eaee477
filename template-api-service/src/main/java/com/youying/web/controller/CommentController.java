package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Comment;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.DeleteFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.system.domain.comment.CommentRequest;
import com.youying.system.domain.comment.CommentResponse;
import com.youying.system.service.CommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * 剧目剧场评论
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/comment")
public class CommentController extends BaseController {

    @Autowired
    private CommentService commentService;

    /**
     * 查询剧目剧场评论列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<CommentResponse>> listByPage(@RequestBody CommentRequest request) {
        startPage(request);
        return R.ok(getTableList(commentService.listByPage(request)));
    }

    /**
     * 查询剧目剧场评论详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<CommentResponse> details(Long id) {
        return R.ok(commentService.details(id));
    }

    /**
     * 举报评论
     *
     * @return
     */
    @PutMapping(value = "/report/{id}")
    @Log(title = "举报评论", businessType = BusinessType.UPDATE)
    public R<?> report(@PathVariable("id") Long id) {
        Comment comment = commentService.findCommentById(id);
        if (comment == null) {
            return R.fail("数据错误");
        }
        if (DeleteFlag.DELETE.getCode().equals(comment.getDeleted())) {
            return R.fail("评论已删除，无法举报。");
        }
        if (StatusFlag.PROHIBITION.getCode().equals(comment.getStatus())) {
            return R.fail("请勿重复举报");
        }
        comment.setStatus(StatusFlag.PROHIBITION.getCode());
        commentService.updateById(comment);
        return R.ok();
    }

    /**
     * 删除剧目剧场评论
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除剧目剧场评论数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(commentService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

