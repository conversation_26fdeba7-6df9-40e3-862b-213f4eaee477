package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.constant.LimitConstants;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.MerchantUser;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.MerchantUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 商家员工表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/merchantUser")
public class MerchantUserController extends BaseController {

    @Autowired
    private MerchantUserService merchantUserService;

    /**
     * 查询商家员工表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<MerchantUser>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(merchantUserService.list()));
    }


    /**
     * 查询商家员工表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<MerchantUser>> list() {
        return R.ok(merchantUserService.list());
    }

    /**
     * 查询商家员工表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<MerchantUser> details(Long id) {
        return R.ok(merchantUserService.getById(id));
    }

    /**
     * 添加商家员工表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加商家员工表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody MerchantUser merchantUser) {
        return R.ok(merchantUserService.save(merchantUser));
    }

    /**
     * 修改商家员工表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改商家员工表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody MerchantUser merchantUser) {
        return R.ok(merchantUserService.updateById(merchantUser));
    }

    /**
     * 修改商家员工密码
     *
     * @return
     */
    @PutMapping(value = "/updatePwd")
    @Log(title = "修改商家员工密码", businessType = BusinessType.UPDATE)
    public R<?> updatePwd(@RequestBody MerchantUser merchantUser) {
        return R.ok(merchantUserService.updatePwd(merchantUser));
    }

    /**
     * 删除商家员工表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除商家员工表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            for (Long id : ids) {
                MerchantUser merchantUser = merchantUserService.getById(id);
                if (merchantUser != null && LimitConstants.ADMIN_FLAG.equals(merchantUser.getType())) {
                    return R.fail("超级管理员无法删除");
                }
            }
            return R.ok(merchantUserService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

