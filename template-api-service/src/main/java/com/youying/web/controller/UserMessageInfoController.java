package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserMessageInfo;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.UserMessageInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 用户消息表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/userMessageInfo")
public class UserMessageInfoController extends BaseController {

    @Autowired
    private UserMessageInfoService userMessageInfoService;

    /**
     * 查询用户消息表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserMessageInfo>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(userMessageInfoService.list()));
    }


    /**
     * 查询用户消息表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<UserMessageInfo>> list() {
        return R.ok(userMessageInfoService.list());
    }

    /**
     * 查询用户消息表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserMessageInfo> details(Long id) {
        return R.ok(userMessageInfoService.getById(id));
    }

    /**
     * 添加用户消息表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加用户消息表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody UserMessageInfo userMessageInfo) {
        return R.ok(userMessageInfoService.save(userMessageInfo));
    }

    /**
     * 修改用户消息表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改用户消息表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody UserMessageInfo userMessageInfo) {
        return R.ok(userMessageInfoService.updateById(userMessageInfo));
    }

    /**
     * 删除用户消息表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除用户消息表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(userMessageInfoService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

