package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.User;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.common.CountResponse;
import com.youying.system.domain.common.ResetPwdRequest;
import com.youying.system.domain.user.UserRequest;
import com.youying.system.domain.user.UserResponse;
import com.youying.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 用户表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/user")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    /**
     * 查询用户表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserResponse>> listByPage(@RequestBody UserRequest request) {
        startPage(request);
        return R.ok(getTableList(userService.listByPage(request)));
    }

    /**
     * 查询用户表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<User> details(Long id) {
        return R.ok(userService.getById(id));
    }

    /**
     * 添加用户表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加用户表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody User user) {
        return R.ok(userService.add(user));
    }

    /**
     * 修改用户表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改用户表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody User user) {
        user.setPassword(null);
        return R.ok(userService.updateById(user));
    }

    /**
     * 修改用户表
     *
     * @return
     */
    @PutMapping(value = "/updateStatus/{id}")
    @Log(title = "修改用户表状态", businessType = BusinessType.UPDATE)
    public R<?> updateStatus(@PathVariable("id") Long id) {
        User user = userService.getById(id);
        if (user == null) {
            return R.fail("用户不存在");
        }
        Integer status = StatusFlag.OK.getCode().equals(user.getStatus()) ? StatusFlag.PROHIBITION.getCode() : StatusFlag.OK.getCode();
        user.setStatus(status);
        return R.ok(userService.updateById(user));
    }

    /**
     * 重置密码
     *
     * @return
     */
    @PutMapping(value = "/resetPwd")
    @Log(title = "重置密码", businessType = BusinessType.UPDATE)
    public R<?> resetPwd(@RequestBody ResetPwdRequest request) {
        User user = userService.getById(request.getId());
        if (user == null) {
            return R.fail("用户不存在");
        }
        user.setPassword(SecurityUtils.encryptPassword(request.getNewPassword()));
        return R.ok(userService.updateById(user));
    }

    /**
     * 用户禁言/解除禁言
     *
     * @return
     */
    @PutMapping(value = "/updateSpeakStatus/{userId}")
    @Log(title = "用户禁言/解除禁言", businessType = BusinessType.UPDATE)
    public R<?> updateSpeakStatus(@PathVariable("userId") Long userId) {
        User userInfo = userService.getById(userId);
        if (userInfo == null) {
            return R.fail("数据错误");
        }
        Integer speakStatus = StatusFlag.OK.getCode().equals(userInfo.getSpeakStatus()) ? StatusFlag.PROHIBITION.getCode() : StatusFlag.OK.getCode();
        userInfo.setSpeakStatus(speakStatus);
        userService.updateById(userInfo);
        return R.ok();
    }

    /**
     * 删除用户表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除用户表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(userService.delete(Arrays.asList(ids)));
        }
        return R.ok();
    }

    /**
     * 首页-查询用户性别数量
     *
     * @return
     */
    @PostMapping(value = "/findUserSexCount")
    public R<List<CountResponse>> findUserSexCount(@RequestBody TimeRequest time) {
        return R.ok(userService.findUserSexCount(time));
    }
}

