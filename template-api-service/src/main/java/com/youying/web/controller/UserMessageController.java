package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.page.TableList;
import com.youying.system.domain.usermessage.UserMessageRequest;
import com.youying.system.domain.usermessage.UserMessageResponse;
import com.youying.system.service.UserMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户会话表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/userMessage")
public class UserMessageController extends BaseController {

    @Autowired
    private UserMessageService userMessageService;

    /**
     * 查询用户会话表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserMessageResponse>> listByPage(@RequestBody UserMessageRequest request) {
        startPage(request);
        return R.ok(getTableList(userMessageService.listByPage(request)));
    }

    /**
     * 查询用户会话表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserMessageResponse> details(Long id) {
        return R.ok(userMessageService.details(id));
    }

}

