package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.PortfolioStatement;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.PortfolioStatementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 免责声明表
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
@RestController
@RequestMapping("/portfolioStatement")
public class PortfolioStatementController extends BaseController {

    @Autowired
    private PortfolioStatementService portfolioStatementService;

    /**
     * 查询免责声明表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<PortfolioStatement>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(portfolioStatementService.list()));
    }

    /**
     * 查询免责声明表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<PortfolioStatement> details(Long id) {
        return R.ok(portfolioStatementService.getById(id));
    }

    /**
     * 修改免责声明表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改免责声明表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody List<PortfolioStatement> portfolioStatement) {
        return R.ok(portfolioStatementService.updateBatchById(portfolioStatement));
    }

}

