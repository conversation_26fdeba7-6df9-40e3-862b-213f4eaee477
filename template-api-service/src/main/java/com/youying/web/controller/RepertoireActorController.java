package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RepertoireActor;
import com.youying.system.service.RepertoireActorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 剧目演员表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/repertoireActor")
public class RepertoireActorController extends BaseController {

    @Autowired
    private RepertoireActorService repertoireActorService;

    /**
     * 根据剧目ID查询演员表列表
     *
     * @param actorType    演员类型（1主演，2群演）
     * @param repertoireId 剧目ID
     * @return
     */
    @GetMapping(value = "/listByRepertoireId")
    public R<List<RepertoireActor>> list(Long repertoireId, Integer actorType) {
        List<RepertoireActor> repertoireActors = repertoireActorService.list(new LambdaQueryWrapper<RepertoireActor>()
                .eq(RepertoireActor::getRepertoireId, repertoireId)
                .eq(RepertoireActor::getActorType, actorType));
        return R.ok(repertoireActors);
    }

    /**
     * 查询剧目演员表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<RepertoireActor> details(Long id) {
        return R.ok(repertoireActorService.getById(id));
    }

}

