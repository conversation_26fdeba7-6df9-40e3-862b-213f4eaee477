package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RepertoireInfoDetail;
import com.youying.common.enums.BusinessType;
import com.youying.system.domain.repertoireinfodetail.RepertoireInfoDetailResponse;
import com.youying.system.service.RepertoireInfoDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 剧目剧场场次表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/repertoireInfoDetail")
public class RepertoireInfoDetailController extends BaseController {

    @Autowired
    private RepertoireInfoDetailService repertoireInfoDetailService;

    /**
     * 根据剧目ID查询场次表列表
     *
     * @return
     */
    @GetMapping(value = "/listByRepertoireId")
    public R<List<RepertoireInfoDetailResponse>> listByRepertoireId(Long repertoireId) {
        return R.ok(repertoireInfoDetailService.listByRepertoireId(repertoireId));
    }

    /**
     * 查询剧目剧场场次表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<RepertoireInfoDetail> details(Long id) {
        return R.ok(repertoireInfoDetailService.getById(id));
    }

    /**
     * 添加剧目剧场场次表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加剧目剧场场次表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody RepertoireInfoDetail repertoireInfoDetail) {
        return R.ok(repertoireInfoDetailService.save(repertoireInfoDetail));
    }

    /**
     * 修改剧目剧场场次表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改剧目剧场场次表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody RepertoireInfoDetail repertoireInfoDetail) {
        return R.ok(repertoireInfoDetailService.updateById(repertoireInfoDetail));
    }

    /**
     * 删除剧目剧场场次表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除剧目剧场场次表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(repertoireInfoDetailService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

