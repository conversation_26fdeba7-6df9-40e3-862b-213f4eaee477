package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.DigitalAvatarImage;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.DigitalAvatarImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 数字头像图片表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/digitalAvatarImage")
public class DigitalAvatarImageController extends BaseController {

    @Autowired
    private DigitalAvatarImageService digitalAvatarImageService;

    /**
     * 查询数字头像图片表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<DigitalAvatarImage>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(digitalAvatarImageService.list()));
    }


    /**
     * 查询数字头像图片表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<DigitalAvatarImage>> list() {
        return R.ok(digitalAvatarImageService.list());
    }

    /**
     * 查询数字头像图片表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<DigitalAvatarImage> details(Long id) {
        return R.ok(digitalAvatarImageService.getById(id));
    }

    /**
     * 添加数字头像图片表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加数字头像图片表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody DigitalAvatarImage digitalAvatarImage) {
        return R.ok(digitalAvatarImageService.save(digitalAvatarImage));
    }

    /**
     * 修改数字头像图片表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改数字头像图片表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody DigitalAvatarImage digitalAvatarImage) {
        return R.ok(digitalAvatarImageService.updateById(digitalAvatarImage));
    }

    /**
     * 删除数字头像图片表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除数字头像图片表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(digitalAvatarImageService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

