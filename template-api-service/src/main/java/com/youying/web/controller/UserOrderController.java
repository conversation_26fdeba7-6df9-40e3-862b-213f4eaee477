package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.utils.excel.EasyPoiUtil;
import com.youying.system.domain.userorder.UserOrderEx;
import com.youying.system.domain.userorder.UserOrderRequest;
import com.youying.system.domain.userorder.UserOrderResponse;
import com.youying.system.service.UserOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户订单表
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@RestController
@RequestMapping("/userOrder")
public class UserOrderController extends BaseController {

    @Autowired
    private UserOrderService userOrderService;

    /**
     * 查询用户订单表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserOrderResponse>> listByPage(@RequestBody UserOrderRequest request) {
        startPage(request);
        return R.ok(getTableList(userOrderService.listByPage(request)));
    }

    /**
     * 查询用户订单表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserOrderResponse> details(Long id) {
        return R.ok(userOrderService.details(id));
    }

    /**
     * 首页-订单金额
     *
     * @return
     */
    @PostMapping(value = "/findOrderSumPrice")
    public R<?> findOrderSumPrice(@RequestBody TimeRequest time) {
        return R.ok(userOrderService.findOrderSumPrice(time));
    }

    /**
     * 导出订单信息
     *
     * @return
     */
    @PostMapping(value = "/export")
    @Log(title = "导出订单信息", businessType = BusinessType.EXPORT)
    public void export(@RequestBody UserOrderRequest request, HttpServletResponse response) {
        List<UserOrderEx> list = userOrderService.export(request);
        EasyPoiUtil.exportExcel(list, UserOrderEx.class, "订单信息", "订单信息", response);
    }
}

