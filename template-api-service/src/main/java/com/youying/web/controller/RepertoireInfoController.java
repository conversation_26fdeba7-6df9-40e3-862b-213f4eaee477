package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RepertoireInfo;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.RelevanceFlag;
import com.youying.system.domain.repertoireinfo.RepertoireInfoRequest;
import com.youying.system.domain.repertoireinfo.RepertoireInfoResponse;
import com.youying.system.service.RepertoireInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 剧目场次信息表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/repertoireInfo")
public class RepertoireInfoController extends BaseController {

    @Autowired
    private RepertoireInfoService repertoireInfoService;

    /**
     * 查询剧目场次信息表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<RepertoireInfoResponse>> listByPage(@RequestBody RepertoireInfoRequest request) {
        startPage(request);
        return R.ok(getTableList(repertoireInfoService.listByPage(request)));
    }

    /**
     * 剧目剧场关联取消、确认
     *
     * @return
     */
    @PutMapping(value = "/updateRelevance")
    @Log(title = "剧目剧场关联取消、确认", businessType = BusinessType.UPDATE)
    public R<?> updateRelevance(@RequestBody RepertoireInfo repertoireInfo) {
        RepertoireInfo info = repertoireInfoService.getById(repertoireInfo.getId());
        if (info == null || RelevanceFlag.CANCEL.getCode().equals(info.getStatus())) {
            return R.fail("已取消，无法操作。");
        }
        return R.ok(repertoireInfoService.updateRelevance(repertoireInfo));
    }
}

