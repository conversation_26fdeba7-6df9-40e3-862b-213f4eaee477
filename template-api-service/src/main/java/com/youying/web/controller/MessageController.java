package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.page.TableList;
import com.youying.system.domain.message.MessageRequest;
import com.youying.system.domain.message.MessageResponse;
import com.youying.system.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 群发消息表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/message")
public class MessageController extends BaseController {

    @Autowired
    private MessageService messageService;

    /**
     * 查询群发消息表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<MessageResponse>> listByPage(@RequestBody MessageRequest request) {
        startPage(request);
        return R.ok(getTableList(messageService.listByPage(request)));
    }

    /**
     * 查询群发消息表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<MessageResponse> details(Long id) {
        return R.ok(messageService.details(id));
    }
}

