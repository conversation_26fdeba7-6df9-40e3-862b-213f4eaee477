package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.page.TableList;
import com.youying.system.domain.autoreply.AutoReplyRequest;
import com.youying.system.domain.autoreply.AutoReplyResponse;
import com.youying.system.service.AutoReplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 剧目剧场自动回复表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/autoReply")
public class AutoReplyController extends BaseController {

    @Autowired
    private AutoReplyService autoReplyService;

    /**
     * 查询剧目剧场自动回复表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<AutoReplyResponse>> listByPage(@RequestBody AutoReplyRequest request) {
        startPage(request);
        return R.ok(getTableList(autoReplyService.listByPage(request)));
    }

}

