package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.SouvenirBadge;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.utils.StringUtils;
import com.youying.system.domain.common.AuditRequest;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeRequest;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeResponse;
import com.youying.system.service.SouvenirBadgeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;

/**
 * 剧场纪念徽章表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/souvenirBadge")
public class SouvenirBadgeController extends BaseController {

    @Autowired
    private SouvenirBadgeService souvenirBadgeService;

    /**
     * 查询剧场纪念徽章表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<SouvenirBadgeResponse>> listByPage(@RequestBody SouvenirBadgeRequest request) {
        startPage(request);
        return R.ok(getTableList(souvenirBadgeService.listByPage(request)));
    }

    /**
     * 剧场纪念徽章审核
     *
     * @return
     */
    @PutMapping(value = "/audit")
    @Log(title = "剧场纪念徽章审核", businessType = BusinessType.UPDATE)
    public R<?> audit(@RequestBody AuditRequest request) {
        SouvenirBadge souvenirBadge = souvenirBadgeService.getById(request.getId());
        if (AuditFlag.PASS.getCode().equals(souvenirBadge.getAudit())) {
            return R.fail("请勿重复审核");
        }
        if (AuditFlag.PASS.getCode().equals(request.getAudit())) {
            souvenirBadge.setAuditPassTime(new Date());
            souvenirBadge.setReasonsRejection(null);
            souvenirBadge.setAudit(AuditFlag.PASS.getCode());
            if (AuditFlag.PASS.getCode().equals(request.getAudit()) && AuditFlag.WAIT.getCode().equals(souvenirBadge.getAuditFlag())) {
                souvenirBadge.setAuditFlag(request.getAudit());
            }
            souvenirBadgeService.sendSouvenirBadge(souvenirBadge);
            souvenirBadgeService.updateById(souvenirBadge);
            return R.ok();
        }
        if (StringUtils.isBlank(request.getReasonsRejection())) {
            return R.fail("驳回原因不能为空");
        }
        souvenirBadge.setAudit(request.getAudit());
        souvenirBadge.setReasonsRejection(request.getReasonsRejection());
        souvenirBadgeService.updateById(souvenirBadge);
        return R.ok();
    }

    /**
     * 删除剧场纪念徽章
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除剧场纪念徽章", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(souvenirBadgeService.delete(Arrays.asList(ids)));
        }
        return R.ok();
    }
}

