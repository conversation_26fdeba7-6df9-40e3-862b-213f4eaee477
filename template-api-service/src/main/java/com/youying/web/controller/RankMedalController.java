package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RankMedal;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.system.domain.common.AuditRequest;
import com.youying.system.domain.rankmedal.RankMedalRequest;
import com.youying.system.domain.rankmedal.RankMedalResponse;
import com.youying.system.service.RankMedalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;

/**
 * 等级勋章表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/rankMedal")
public class RankMedalController extends BaseController {

    @Autowired
    private RankMedalService rankMedalService;

    /**
     * 查询等级勋章表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<RankMedalResponse>> listByPage(@RequestBody RankMedalRequest request) {
        startPage(request);
        return R.ok(getTableList(rankMedalService.listByPage(request)));
    }

    /**
     * 查询等级勋章表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<RankMedalResponse> details(Long id) {
        return R.ok(rankMedalService.details(id));
    }

    /**
     * 剧场纪念徽章审核
     *
     * @return
     */
    @PutMapping(value = "/audit")
    @Log(title = "纪念徽章审核", businessType = BusinessType.UPDATE)
    public R<?> audit(@RequestBody AuditRequest request) {
        RankMedal rankMedal = rankMedalService.getById(request.getId());
        if (AuditFlag.PASS.getCode().equals(rankMedal.getAudit())) {
            return R.fail("请勿重复审核");
        }
        if (AuditFlag.PASS.getCode().equals(request.getAudit())) {
            rankMedal.setAuditPassTime(new Date());
            rankMedal.setReasonsRejection(null);
            rankMedal.setAudit(AuditFlag.PASS.getCode());
            if (AuditFlag.PASS.getCode().equals(request.getAudit()) && AuditFlag.WAIT.getCode().equals(rankMedal.getAuditFlag())) {
                rankMedal.setAuditFlag(request.getAudit());
            } else if (AuditFlag.PASS.getCode().equals(rankMedal.getAuditFlag())) {
                rankMedal.setLastUpdateTime(new Date());
            }
            // 判断纪念徽章是否有合适用户推送
            rankMedalService.sendRankMedal(rankMedal);
        }

        rankMedal.setAudit(request.getAudit());
        rankMedal.setReasonsRejection(request.getReasonsRejection());
        rankMedalService.updateById(rankMedal);
        return R.ok();
    }

    /**
     * 删除等级勋章表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除等级勋章表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(rankMedalService.delete(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

