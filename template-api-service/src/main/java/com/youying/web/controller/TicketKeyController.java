package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.TicketKey;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.TicketKeyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * 纸质票关键字
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@RestController
@RequestMapping("/ticketKey")
public class TicketKeyController extends BaseController {

    @Autowired
    private TicketKeyService ticketKeyService;

    /**
     * 查询纸质票关键字
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<TicketKey>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(ticketKeyService.listByPage(pageDomain)));
    }

    /**
     * 查询纸质票关键字详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<TicketKey> details(Long id) {
        return R.ok(ticketKeyService.getById(id));
    }

    /**
     * 添加纸质票关键字
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加纸质票关键字数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody TicketKey ticketKey) {
        return R.ok(ticketKeyService.save(ticketKey));
    }

    /**
     * 修改纸质票关键字
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改纸质票关键字数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody TicketKey ticketKey) {
        return R.ok(ticketKeyService.updateById(ticketKey));
    }

    /**
     * 删除纸质票关键字
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除纸质票关键字数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(ticketKeyService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

