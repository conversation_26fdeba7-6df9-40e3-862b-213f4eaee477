package com.youying.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.system.domain.portfolio.PortfolioRequest;
import com.youying.system.domain.portfolio.PortfolioResponse;
import com.youying.system.service.PortfolioService;

/**
 * 藏品组合表
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@RestController
@RequestMapping("/portfolio")
public class PortfolioController extends BaseController {

    @Autowired
    private PortfolioService portfolioService;

    /**
     * 添加藏品组合表
     *
     * @return
     */
    @PostMapping(value = "/add")
    public R<?> add(@RequestBody Portfolio portfolio) {
        return R.ok(portfolioService.save(portfolio));
    }

    /**
     * 查询藏品组合表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<PortfolioResponse>> listByPage(@RequestBody PortfolioRequest request) {
        startPage(request);
        return R.ok(getTableList(portfolioService.listByPage(request)));
    }

    /**
     * 查询藏品组合表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<PortfolioResponse> details(Long id) {
        return R.ok(portfolioService.details(id));
    }

    /**
     * 修改藏品组合
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改藏品组合", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody Portfolio portfolio) {
        return R.ok(portfolioService.update(portfolio));
    }

    /**
     * 删除藏品组合表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除藏品组合表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(portfolioService.delete(ids));
        }
        return R.ok();
    }

    /**
     * 商品信息审核
     *
     * @return
     */
    @PutMapping(value = "/audit")
    @Log(title = "商品信息审核", businessType = BusinessType.UPDATE)
    public R<?> audit(@RequestBody Portfolio portfolio) {
        Portfolio portfolioInfo = portfolioService.getById(portfolio.getId());
        if (AuditFlag.PASS.getCode().equals(portfolioInfo.getAudit())) {
            return R.fail("请勿重复审核");
        }
        return R.ok(portfolioService.audit(portfolio));
    }

    /**
     * 修改商品电子票、数字头显展示
     *
     * @return
     */
    @PutMapping(value = "/updateLookStatus/{id}")
    @Log(title = "修改商品电子票、数字头显展示", businessType = BusinessType.UPDATE)
    public R<?> updateLookStatus(@PathVariable("id") Long id) {
        Portfolio portfolioInfo = portfolioService.getById(id);
        Integer lookStatus = StatusFlag.toggleStatus(portfolioInfo.getLookStatus());
        portfolioInfo.setLookStatus(lookStatus);
        return R.ok(portfolioService.updateById(portfolioInfo));
    }

    /**
     * 修改商品座位是否唯一
     *
     * @return
     */
    @PutMapping(value = "/updateSeatStatus/{id}")
    @Log(title = "修改商品座位是否唯一", businessType = BusinessType.UPDATE)
    public R<?> updateSeatStatus(@PathVariable("id") Long id) {
        Portfolio portfolioInfo = portfolioService.getById(id);
        Integer seatStatus = StatusFlag.toggleStatus(portfolioInfo.getSeatStatus());
        portfolioInfo.setSeatStatus(seatStatus);
        return R.ok(portfolioService.updateById(portfolioInfo));
    }
}
