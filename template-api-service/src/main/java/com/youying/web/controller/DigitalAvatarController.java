package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.DigitalAvatar;
import com.youying.common.core.domain.entity.DigitalAvatarImage;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.system.domain.common.AuditRequest;
import com.youying.system.domain.digitalavatar.DigitalAvatarRequest;
import com.youying.system.domain.digitalavatar.DigitalAvatarResponse;
import com.youying.system.service.DigitalAvatarImageService;
import com.youying.system.service.DigitalAvatarService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 数字头像表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/digitalAvatar")
public class DigitalAvatarController extends BaseController {
    @Autowired
    private DigitalAvatarService digitalAvatarService;
    @Autowired
    private DigitalAvatarImageService digitalAvatarImageService;

    /**
     * 查询数字头像表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<DigitalAvatarResponse>> listByPage(@RequestBody DigitalAvatarRequest request) {
        startPage(request);
        return R.ok(getTableList(digitalAvatarService.listByPage(request)));
    }

    /**
     * 数字头像审核
     *
     * @return
     */
    @PutMapping(value = "/audit")
    @Log(title = "数字头像审核", businessType = BusinessType.UPDATE)
    public R<?> audit(@RequestBody AuditRequest request) throws IOException {
        DigitalAvatar digitalAvatar = digitalAvatarService.getById(request.getId());
        List<DigitalAvatarImage> imageByDigitalAvatar = digitalAvatarImageService.findAvatarImageByDigitalAvatar(request.getId());
        if (CollectionUtils.isEmpty(imageByDigitalAvatar)) {
            return R.fail("数字头像组件不能为空");
        }
        if (AuditFlag.PASS.getCode().equals(digitalAvatar.getAudit())) {
            return R.fail("请勿重复审核");
        }
        digitalAvatarService.audit(request);
        return R.ok();
    }

    /**
     * 查询数字头像表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<DigitalAvatarResponse> details(Long id) {
        return R.ok(digitalAvatarService.details(id));
    }

    /**
     * 删除数字头像表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除数字头像表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(digitalAvatarService.delete(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

