package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.page.TableList;
import com.youying.system.domain.user.UserRequest;
import com.youying.system.domain.userleaderboard.UserLeaderboardResponse;
import com.youying.system.service.UserLeaderboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户排行榜表
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@RestController
@RequestMapping("/userLeaderboard")
public class UserLeaderboardController extends BaseController {

    @Autowired
    private UserLeaderboardService userLeaderboardService;

    /**
     * 查询用户排行榜表
     *
     * @return
     */
    @PostMapping(value = "/findUserLeaderboard")
    public R<TableList<UserLeaderboardResponse>> findUserLeaderboard(@RequestBody UserRequest request) {
        startPage(request);
        return R.ok(getTableList(userLeaderboardService.findUserLeaderboard(request)));
    }

}

