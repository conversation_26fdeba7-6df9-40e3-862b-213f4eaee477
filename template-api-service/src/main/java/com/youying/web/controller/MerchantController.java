package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Merchant;
import com.youying.common.core.domain.entity.MerchantUser;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.domain.common.PullRequest;
import com.youying.system.domain.merchant.MerchantRequest;
import com.youying.system.domain.merchant.MerchantResponse;
import com.youying.system.service.MerchantService;
import com.youying.system.service.MerchantUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 商家表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/merchant")
public class MerchantController extends BaseController {
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private MerchantUserService merchantUserService;

    /**
     * 查询商家表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<MerchantResponse>> listByPage(@RequestBody MerchantRequest request) {
        startPage(request);
        return R.ok(getTableList(merchantService.listByPage(request)));
    }

    /**
     * 查询商家表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<Merchant> details(Long id) {
        return R.ok(merchantService.getById(id));
    }

    /**
     * 添加商家表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加商家表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody Merchant merchant) {
        Boolean flag = merchantService.findMerchantExist(null, merchant.getAccount(), merchant.getMerchantCategory());
        MerchantUser merchantUser = merchantUserService.findMerchantUserExist(merchant.getAccount(), merchant.getMerchantCategory());
        if (flag || merchantUser != null) {
            return R.fail("账号" + merchant.getAccount() + "已存在");
        }
        return R.ok(merchantService.add(merchant));
    }

    /**
     * 修改商家表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改商家表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody Merchant merchant) {
        Merchant merchantInfo = merchantService.getById(merchant.getId());
        merchant.setPassword(null);
        merchant.setMerchantCategory(null);

        Boolean flag = merchantService.findMerchantExist(merchant.getId(), merchant.getAccount(), merchantInfo.getMerchantCategory());
        MerchantUser merchantUser = merchantUserService.findMerchantUserExist(merchant.getAccount(), merchantInfo.getMerchantCategory());

        if (flag || (merchantUser != null && !merchantInfo.getMerchantUserId().equals(merchantUser.getId()))) {
            return R.fail("账号" + merchant.getAccount() + "已存在");
        }
        MerchantUser merchantUserInfo = merchantUserService.getById(merchantInfo.getMerchantUserId());
        merchantUserInfo.setPhone(merchant.getAccount());
        merchantUserService.updateById(merchantUserInfo);

        return R.ok(merchantService.updateById(merchant));
    }

    /**
     * 删除商家表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除商家表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(merchantService.delete(Arrays.asList(ids)));
        }
        return R.ok();
    }

    /**
     * 商家下拉
     *
     * @return
     */
    @PostMapping(value = "/pull")
    public R<List<PullResponse>> pull(@RequestBody PullRequest request) {
        return R.ok(merchantService.pull(request));
    }

    /**
     * 首页-查询商家增量
     *
     * @return
     */
    @PostMapping(value = "/findMerchantAddCount")
    public R<?> findMerchantAddCount(@RequestBody TimeRequest time) {
        return R.ok(merchantService.findMerchantAddCount(time));
    }

}

