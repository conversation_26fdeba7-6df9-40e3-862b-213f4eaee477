package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.common.core.domain.entity.SouvenirBadge;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.BadgeTypeFlag;
import com.youying.common.utils.excel.EasyPoiUtil;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsEx;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsRequest;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsResponse;
import com.youying.system.service.RepertoireTicketService;
import com.youying.system.service.SouvenirBadgeService;
import com.youying.system.service.UserReceivingRecordsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户数字藏品领取记录
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/userReceivingRecords")
public class UserReceivingRecordsController extends BaseController {

    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;
    @Autowired
    private SouvenirBadgeService souvenirBadgeService;
    @Autowired
    private RepertoireTicketService repertoireTicketService;

    /**
     * 查询用户数字藏品领取记录列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserReceivingRecordsResponse>> listByPage(@RequestBody UserReceivingRecordsRequest request) {
        startPage(request);
        return R.ok(getTableList(userReceivingRecordsService.listByPage(request)));
    }

    /**
     * 查询用户数字藏品领取记录详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserReceivingRecordsResponse> details(Long id) {
        UserReceivingRecordsResponse response = new UserReceivingRecordsResponse();
        UserReceivingRecords userReceivingRecords = userReceivingRecordsService.getById(id);
        BeanUtils.copyProperties(userReceivingRecords, response);
        if (BadgeTypeFlag.SOUVENIR_BADGE.getCode().equals(userReceivingRecords.getBadgeType())) {
            SouvenirBadge souvenirBadge = souvenirBadgeService.getById(userReceivingRecords.getRelationId());
            response.setImage(souvenirBadge.getCoverPicture());
        }
        if (BadgeTypeFlag.ELECTRONIC_TICKET.getCode().equals(userReceivingRecords.getBadgeType())) {
            RepertoireTicket repertoireTicket = repertoireTicketService.getById(userReceivingRecords.getRelationId());
            response.setCoverFront(repertoireTicket.getCoverFront());
            response.setCoverReverse(repertoireTicket.getCoverReverse());
        }
        return R.ok(response);
    }

    /**
     * 查询藏品领取人详情
     *
     * @return
     */
    @PostMapping(value = "/findCollectionRecipient")
    public R<TableList<UserReceivingRecordsResponse>> findCollectionRecipient(@RequestBody UserReceivingRecordsRequest request) {
        startPage(request);
        return R.ok(getTableList(userReceivingRecordsService.findCollectionRecipient(request)));
    }

    /**
     * 首页-查询用户领取数字藏品数
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/findUserReceivingRecordsAddCount")
    public R<?> findUserReceivingRecordsAddCount(@RequestBody UserReceivingRecordsRequest request) {
        return R.ok(userReceivingRecordsService.findUserReceivingRecordsAddCount(request.getTime(), Integer.valueOf(request.getBadgeType())));
    }

    /**
     * 修改座位
     *
     * @return
     */
    @PostMapping(value = "/updateSeat")
    @Log(title = "修改座位", businessType = BusinessType.EXPORT)
    public R<?> updateSeat(@RequestBody UserReceivingRecords userReceivingRecords) {
        return R.ok(userReceivingRecordsService.updateById(userReceivingRecords));
    }

    /**
     * 导出消费记录
     *
     * @return
     */
    @PostMapping(value = "/export")
    @Log(title = "导出消费记录", businessType = BusinessType.EXPORT)
    public void export(@RequestBody UserReceivingRecordsRequest request, HttpServletResponse response) {
        List<UserReceivingRecordsEx> list = userReceivingRecordsService.export(request);
        EasyPoiUtil.exportExcel(list, UserReceivingRecordsEx.class, "消费记录", "消费记录", response);
    }

}

