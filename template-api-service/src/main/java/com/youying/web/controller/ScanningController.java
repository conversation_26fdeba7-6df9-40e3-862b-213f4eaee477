package com.youying.web.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.youying.common.annotation.Log;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Scanning;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.DefaultFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.system.domain.scanning.AddScanningRequest;
import com.youying.system.domain.scanning.ScanningRequest;
import com.youying.system.domain.scanning.ScanningResponse;
import com.youying.system.service.PortfolioService;
import com.youying.system.service.ScanningService;

/**
 * 纸制票扫描规则
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@RestController
@RequestMapping("/scanning")
public class ScanningController extends BaseController {
    @Autowired
    private ScanningService scanningService;
    @Autowired
    private PortfolioService portfolioService;

    /**
     * 查询列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<Scanning>> listByPage(@RequestBody ScanningRequest request) {
        startPage(request);
        List<ScanningResponse> list = scanningService.listByPage(request);
        return R.ok(getTableList(list));
    }

    /**
     * 查询详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<ScanningResponse> details(Long id) {
        return R.ok(scanningService.details(id));
    }

    /**
     * 查询详情
     *
     * @return
     */
    @GetMapping(value = "/pull")
    public R<List<PullResponse>> pull() {
        return R.ok(scanningService.pull());
    }

    /**
     * 添加纸制票扫描规则
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加纸制票扫描规则", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody AddScanningRequest request) {
        return R.ok(scanningService.add(request));
    }

    /**
     * 修改纸制票扫描规则
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改纸制票扫描规则", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody AddScanningRequest scanning) {
        return R.ok(scanningService.update(scanning));
    }

    /**
     * 修改纸制票扫描规则状态
     *
     * @return
     */
    @PutMapping(value = "/updateStatus/{scanningId}")
    @Log(title = "修改纸制票扫描规则", businessType = BusinessType.UPDATE)
    public R<?> updateStatus(@PathVariable("scanningId") Long scanningId) {
        Scanning scanning = scanningService.getById(scanningId);
        if (DefaultFlag.DEFAULT.getCode().equals(scanning.getDefaultFlag())) {
            return R.fail("默认规则无法修改");
        }
        Long count = portfolioService.findScanningCount(scanningId);
        if (count > 0 && StatusFlag.OK.getCode().equals(scanning.getStatus())) {
            return R.fail("存在商品使用该规则，无法禁用");
        }
        Integer status = StatusFlag.toggleStatus(scanning.getStatus());
        scanning.setStatus(status);
        scanningService.updateById(scanning);
        return R.ok();
    }

    /**
     * 删除
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除纸制票扫描规则", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(scanningService.delete(ids));
        }
        return R.ok();
    }
}
