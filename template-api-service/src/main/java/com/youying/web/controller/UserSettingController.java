package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserSetting;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.UserSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 用户设置表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/userSetting")
public class UserSettingController extends BaseController {

    @Autowired
    private UserSettingService userSettingService;

    /**
     * 查询用户设置表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserSetting>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(userSettingService.list()));
    }


    /**
     * 查询用户设置表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<UserSetting>> list() {
        return R.ok(userSettingService.list());
    }

    /**
     * 查询用户设置表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserSetting> details(Long id) {
        return R.ok(userSettingService.getById(id));
    }

    /**
     * 添加用户设置表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加用户设置表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody UserSetting userSetting) {
        return R.ok(userSettingService.save(userSetting));
    }

    /**
     * 修改用户设置表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改用户设置表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody UserSetting userSetting) {
        return R.ok(userSettingService.updateById(userSetting));
    }

    /**
     * 删除用户设置表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除用户设置表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(userSettingService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

