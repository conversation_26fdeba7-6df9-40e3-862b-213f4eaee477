package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Leaderboard;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.DataFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.system.domain.leaderboard.LeaderboardRequest;
import com.youying.system.domain.userleaderboard.LeaderboardResponse;
import com.youying.system.service.LeaderboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * 榜单
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@RestController
@RequestMapping("/leaderboard")
public class LeaderboardController extends BaseController {

    @Autowired
    private LeaderboardService leaderboardService;

    /**
     * 查询系统榜单
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<Leaderboard>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(leaderboardService.listByPage(pageDomain)));
    }

    /**
     * 榜单剧目统计
     *
     * @return
     */
    @PostMapping(value = "/findRepertoireLeaderboard")
    public R<TableList<LeaderboardResponse>> findRepertoireLeaderboard(@RequestBody LeaderboardRequest request) {
        startPage(request);
        return R.ok(getTableList(leaderboardService.findRepertoireLeaderboard(request)));
    }

    /**
     * 查询榜单详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<Leaderboard> details(Long id) {
        return R.ok(leaderboardService.getById(id));
    }

    /**
     * 添加榜单
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加榜单数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody Leaderboard leaderboard) {
        leaderboard.setUserId(0L);
        leaderboard.setType(DataFlag.ADMIN.getCode());
        return R.ok(leaderboardService.save(leaderboard));
    }

    /**
     * 修改榜单
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改榜单数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody Leaderboard leaderboard) {
        Leaderboard leaderboardInfo = leaderboardService.getById(leaderboard.getId());
        if (leaderboardInfo == null || !DataFlag.ADMIN.getCode().equals(leaderboardInfo.getType())) {
            return R.fail("无法修改用户榜单");
        }
        return R.ok(leaderboardService.updateById(leaderboard));
    }

    /**
     * 修改榜单状态
     *
     * @return
     */
    @PutMapping(value = "/updateStatus/{id}")
    @Log(title = "修改榜单状态", businessType = BusinessType.UPDATE)
    public R<?> updateStatus(@PathVariable("id") Long id) {
        Leaderboard leaderboardInfo = leaderboardService.getById(id);
        if (leaderboardInfo == null || !DataFlag.ADMIN.getCode().equals(leaderboardInfo.getType())) {
            return R.fail("无法修改用户榜单");
        }
        Integer status = StatusFlag.toggleStatus(leaderboardInfo.getStatus());
        leaderboardInfo.setStatus(status);
        return R.ok(leaderboardService.updateById(leaderboardInfo));
    }

    /**
     * 删除榜单
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除榜单数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            long count = leaderboardService.count(new LambdaQueryWrapper<Leaderboard>()
                    .in(Leaderboard::getId, ids)
                    .eq(Leaderboard::getType, DataFlag.USER.getCode()));
            if (count > 0) {
                return R.fail("无法删除用户榜单");
            }
            return R.ok(leaderboardService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

