package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.AdvertisingPicture;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.system.domain.advertisingpicture.AdvertisingPictureRequest;
import com.youying.system.service.AdvertisingPictureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * 广告轮播图表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/advertisingPicture")
public class AdvertisingPictureController extends BaseController {

    @Autowired
    private AdvertisingPictureService advertisingPictureService;

    /**
     * 查询广告轮播图表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<AdvertisingPicture>> listByPage(@RequestBody AdvertisingPictureRequest request) {
        startPage(request);
        return R.ok(getTableList(advertisingPictureService.listByPage(request)));
    }

    /**
     * 查询广告轮播图表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<AdvertisingPicture> details(Long id) {
        return R.ok(advertisingPictureService.getById(id));
    }

    /**
     * 添加广告轮播图表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加广告轮播图表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody AdvertisingPicture advertisingPicture) {
        return R.ok(advertisingPictureService.save(advertisingPicture));
    }

    /**
     * 修改广告轮播图表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改广告轮播图表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody AdvertisingPicture advertisingPicture) {
        return R.ok(advertisingPictureService.updateById(advertisingPicture));
    }

    /**
     * 修改广告轮播图状态
     *
     * @return
     */
    @PutMapping(value = "/updateStatus/{id}")
    @Log(title = "修改广告轮播图状态", businessType = BusinessType.UPDATE)
    public R<?> updateStatus(@PathVariable("id") Long id) {
        AdvertisingPicture advertisingPicture = advertisingPictureService.getById(id);
        Integer status = StatusFlag.OK.getCode().equals(advertisingPicture.getStatus()) ? StatusFlag.PROHIBITION.getCode() : StatusFlag.OK.getCode();
        advertisingPicture.setStatus(status);
        return R.ok(advertisingPictureService.updateById(advertisingPicture));
    }

    /**
     * 删除广告轮播图表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除广告轮播图表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(advertisingPictureService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

