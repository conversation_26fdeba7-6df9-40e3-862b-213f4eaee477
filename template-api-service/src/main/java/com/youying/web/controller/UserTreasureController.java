package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserTreasure;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.utils.excel.EasyPoiUtil;
import com.youying.system.domain.common.CountResponse;
import com.youying.system.domain.usertreasure.UserTreasureEx;
import com.youying.system.domain.usertreasure.UserTreasureRequest;
import com.youying.system.domain.usertreasure.UserTreasureResponse;
import com.youying.system.service.UserTreasureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 用户剧目剧场收藏表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/userTreasure")
public class UserTreasureController extends BaseController {

    @Autowired
    private UserTreasureService userTreasureService;

    /**
     * 查询用户剧目剧场收藏表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserTreasure>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(userTreasureService.list()));
    }


    /**
     * 查询用户剧目剧场收藏表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<UserTreasure>> list() {
        return R.ok(userTreasureService.list());
    }

    /**
     * 查询用户剧目剧场收藏表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserTreasure> details(Long id) {
        return R.ok(userTreasureService.getById(id));
    }

    /**
     * 添加用户剧目剧场收藏表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加用户剧目剧场收藏表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody UserTreasure userTreasure) {
        return R.ok(userTreasureService.save(userTreasure));
    }

    /**
     * 修改用户剧目剧场收藏表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改用户剧目剧场收藏表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody UserTreasure userTreasure) {
        return R.ok(userTreasureService.updateById(userTreasure));
    }

    /**
     * 删除用户剧目剧场收藏表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除用户剧目剧场收藏表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(userTreasureService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

    /**
     * 首页-查询剧目剧场关注人数
     *
     * @return
     */
    @PostMapping(value = "/findUserTreasureAddCount")
    public R<List<UserTreasureResponse>> findUserTreasureAddCount(@RequestBody TimeRequest time) {
        return R.ok(userTreasureService.findUserTreasureAddCount(time));
    }

    /**
     * 首页-查询剧目关注人数
     *
     * @return
     */
    @PostMapping(value = "/findUserTreasureRepertoireCount")
    public R<List<CountResponse>> findUserTreasureRepertoireCount(@RequestBody TimeRequest time) {
        return R.ok(userTreasureService.findUserTreasureRepertoireCount(time));
    }


    /**
     * 首页-查询剧场关注人数
     *
     * @return
     */
    @PostMapping(value = "/findUserTreasureTheaterCount")
    public R<List<CountResponse>> findUserTreasureTheaterCount(@RequestBody TimeRequest time) {
        return R.ok(userTreasureService.findUserTreasureTheaterCount(time));
    }

    /**
     * 查询关注用户统计(剧目)
     *
     * @return
     */
    @PostMapping(value = "/findRepertoireFans")
    public R<TableList<UserTreasureResponse>> findRepertoireFans(@RequestBody UserTreasureRequest request) {
        startPage(request);
        return R.ok(getTableList(userTreasureService.findRepertoireFans(request)));
    }

    /**
     * 查询关注用户统计(剧场)
     *
     * @return
     */
    @PostMapping(value = "/findTheaterFans")
    public R<TableList<UserTreasureResponse>> findTheaterFans(@RequestBody UserTreasureRequest request) {
        startPage(request);
        return R.ok(getTableList(userTreasureService.findTheaterFans(request)));
    }

    /**
     * 导出关注用户统计(剧目)
     *
     * @return
     */
    @PostMapping(value = "/exportRepertoireFans")
    @Log(title = "导出关注用户统计(剧目)", businessType = BusinessType.EXPORT)
    public void exportRepertoireFans(@RequestBody UserTreasureRequest request, HttpServletResponse response) {
        List<UserTreasureEx> list = userTreasureService.exportRepertoireFans(request);
        EasyPoiUtil.exportExcel(list, UserTreasureEx.class, "关注用户统计(剧目)", "关注用户统计(剧目)", response);
    }

    /**
     * 导出关注用户统计(剧场)
     *
     * @return
     */
    @PostMapping(value = "/exportTheaterFans")
    @Log(title = "导出关注用户统计(剧场)", businessType = BusinessType.EXPORT)
    public void exportTheaterFans(@RequestBody UserTreasureRequest request, HttpServletResponse response) {
        List<UserTreasureEx> list = userTreasureService.exportTheaterFans(request);
        EasyPoiUtil.exportExcel(list, UserTreasureEx.class, "关注用户统计(剧场)", "关注用户统计(剧场)", response);
    }

}

