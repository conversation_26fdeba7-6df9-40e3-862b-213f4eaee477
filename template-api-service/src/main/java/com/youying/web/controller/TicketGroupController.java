package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.TicketGroup;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.DataFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.system.service.TicketGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * 电子票分组
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@RestController
@RequestMapping("/ticketGroup")
public class TicketGroupController extends BaseController {

    @Autowired
    private TicketGroupService ticketGroupService;

    /**
     * 查询电子票分组
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<TicketGroup>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(ticketGroupService.listByPage(pageDomain)));
    }

    /**
     * 查询电子票分组详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<TicketGroup> details(Long id) {
        return R.ok(ticketGroupService.getById(id));
    }

    /**
     * 添加电子票分组
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加电子票分组数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody TicketGroup ticketGroup) {
        ticketGroup.setType(DataFlag.ADMIN.getCode());
        ticketGroup.setUserId(0L);
        return R.ok(ticketGroupService.save(ticketGroup));
    }

    /**
     * 修改电子票分组
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改电子票分组数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody TicketGroup ticketGroup) {
        TicketGroup ticketGroupInfo = ticketGroupService.getById(ticketGroup.getId());
        if (ticketGroupInfo == null || !DataFlag.ADMIN.getCode().equals(ticketGroupInfo.getType())) {
            return R.fail("无法修改电子票分组");
        }
        return R.ok(ticketGroupService.updateById(ticketGroup));
    }

    /**
     * 电子票分组状态
     *
     * @return
     */
    @PutMapping(value = "/updateStatus/{id}")
    @Log(title = "修改电子  票分组状态", businessType = BusinessType.UPDATE)
    public R<?> updateStatus(@PathVariable("id") Long id) {
        TicketGroup ticketGroup = ticketGroupService.getById(id);
        if (ticketGroup == null || !DataFlag.ADMIN.getCode().equals(ticketGroup.getType())) {
            return R.fail("无法修改电子票分组");
        }
        Integer status = StatusFlag.toggleStatus(ticketGroup.getStatus());
        ticketGroup.setStatus(status);
        return R.ok(ticketGroupService.updateById(ticketGroup));
    }

    /**
     * 删除电子票分组
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{id}")
    @Log(title = "删除电子票分组数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("id") Long[] ids) {
        if (ids.length > 0) {
            for (Long id : ids) {
                if (id <= 3) {
                    return R.fail("无法删除电子票分组");
                }
            }
            long count = ticketGroupService.count(new LambdaQueryWrapper<TicketGroup>()
                    .in(TicketGroup::getId, ids)
                    .eq(TicketGroup::getType, DataFlag.USER.getCode()));
            if (count > 0) {
                return R.fail("无法删除电子票分组");
            }
            return R.ok(ticketGroupService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

