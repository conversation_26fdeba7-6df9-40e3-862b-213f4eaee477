package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.SouvenirBadgeRequire;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.SouvenirBadgeRequireService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 纪念徽章领取规则表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/souvenirBadgeRequire")
public class SouvenirBadgeRequireController extends BaseController {

    @Autowired
    private SouvenirBadgeRequireService souvenirBadgeRequireService;

    /**
     * 查询纪念徽章领取规则表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<SouvenirBadgeRequire>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(souvenirBadgeRequireService.list()));
    }


    /**
     * 查询纪念徽章领取规则表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<SouvenirBadgeRequire>> list() {
        return R.ok(souvenirBadgeRequireService.list());
    }

    /**
     * 查询纪念徽章领取规则表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<SouvenirBadgeRequire> details(Long id) {
        return R.ok(souvenirBadgeRequireService.getById(id));
    }

    /**
     * 添加纪念徽章领取规则表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加纪念徽章领取规则表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody SouvenirBadgeRequire souvenirBadgeRequire) {
        return R.ok(souvenirBadgeRequireService.save(souvenirBadgeRequire));
    }

    /**
     * 修改纪念徽章领取规则表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改纪念徽章领取规则表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody SouvenirBadgeRequire souvenirBadgeRequire) {
        return R.ok(souvenirBadgeRequireService.updateById(souvenirBadgeRequire));
    }

    /**
     * 删除纪念徽章领取规则表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除纪念徽章领取规则表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(souvenirBadgeRequireService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

