package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.WechatSetting;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.WechatSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序设置表
 *
 * <AUTHOR>
 * @since 2023-06-25
 */
@RestController
@RequestMapping("/wechatSetting")
public class WechatSettingController extends BaseController {

    @Autowired
    private WechatSettingService wechatSettingService;

    /**
     * 查询小程序设置表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<WechatSetting> details() {
        return R.ok(wechatSettingService.getById(1));
    }

    /**
     * 修改小程序设置表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改小程序设置表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody WechatSetting wechatSetting) {
        return R.ok(wechatSettingService.updateById(wechatSetting));
    }

}

