package com.youying.web.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserLeaderboardComment;
import com.youying.system.service.UserLeaderboardCommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户榜单评论表
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@RestController
@RequestMapping("/userLeaderboardComment")
public class UserLeaderboardCommentController extends BaseController {

    @Autowired
    private UserLeaderboardCommentService userLeaderboardCommentService;

    /**
     * 查询用户榜单评论表
     *
     * @return
     */
    @GetMapping(value = "/findUserLeaderboardComment")
    public R<List<UserLeaderboardComment>> findUserLeaderboardComment(Long userId) {
        return R.ok(userLeaderboardCommentService.list(new LambdaQueryWrapper<UserLeaderboardComment>()
                .eq(UserLeaderboardComment::getUserId, userId)));
    }
}

