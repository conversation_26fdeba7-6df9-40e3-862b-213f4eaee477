package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Issue;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.system.domain.issue.IssueRequest;
import com.youying.system.domain.issue.IssueResponse;
import com.youying.system.service.IssueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 剧目剧场问答表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/issue")
public class IssueController extends BaseController {

    @Autowired
    private IssueService issueService;

    /**
     * 查询剧目剧场问答表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<Issue>> listByPage(@RequestBody IssueRequest request) {
        startPage(request);
        return R.ok(getTableList(issueService.listByPage(request)));
    }

    /**
     * 查询剧目剧场问答表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<IssueResponse> details(Long id) {
        return R.ok(issueService.details(id));
    }

    /**
     * 举报问答
     *
     * @return
     */
    @PutMapping(value = "/report/{id}")
    @Log(title = "举报问答", businessType = BusinessType.UPDATE)
    public R<?> report(@PathVariable("id") Long id) {
        Issue issue = issueService.getById(id);
        if (issue == null) {
            return R.fail("数据错误");
        }
        if (StatusFlag.PROHIBITION.getCode().equals(issue.getStatus())) {
            return R.fail("请勿重复举报");
        }
        issue.setStatus(StatusFlag.PROHIBITION.getCode());
        issueService.updateById(issue);
        return R.ok();
    }

}
