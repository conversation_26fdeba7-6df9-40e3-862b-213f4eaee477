package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.CommentInfo;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.CommentInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 评论点赞、踩详情表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/commentInfo")
public class CommentInfoController extends BaseController {

    @Autowired
    private CommentInfoService commentInfoService;

    /**
     * 查询评论点赞、踩详情表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<CommentInfo>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(commentInfoService.list()));
    }

    /**
     * 查询评论点赞、踩详情表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<CommentInfo>> list() {
        return R.ok(commentInfoService.list());
    }

    /**
     * 查询评论点赞、踩详情表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<CommentInfo> details(Long id) {
        return R.ok(commentInfoService.getById(id));
    }

    /**
     * 添加评论点赞、踩详情表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加评论点赞、踩详情表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody CommentInfo commentInfo) {
        return R.ok(commentInfoService.save(commentInfo));
    }

    /**
     * 修改评论点赞、踩详情表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改评论点赞、踩详情表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody CommentInfo commentInfo) {
        return R.ok(commentInfoService.updateById(commentInfo));
    }

    /**
     * 删除评论点赞、踩详情表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除评论点赞、踩详情表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(commentInfoService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

