package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Area;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.domain.common.area.Tree;
import com.youying.system.service.AreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 地区表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/area")
public class AreaController extends BaseController {

    @Autowired
    private AreaService areaService;

    /**
     * 查询地区表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<Area>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(areaService.list()));
    }

    /**
     * 查询地区表树形
     *
     * @return
     */
    @GetMapping(value = "/findAreaTree")
    public R<List<Tree>> findAreaTree() {
        return R.ok(areaService.findAreaTree());
    }

    /**
     * 查询地区表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<Area> details(Long id) {
        return R.ok(areaService.details(id));
    }

    /**
     * 添加地区表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加地区表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody Area area) {
        areaService.save(area);
        return R.ok(area.getId());
    }

    /**
     * 修改地区表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改地区表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody Area area) {
        return R.ok(areaService.updateById(area));
    }

    /**
     * 删除地区表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除地区表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(areaService.removeByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

