package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.Agreement;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.AgreementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 协议设置表
 *
 * <AUTHOR>
 * @since 2023-06-25
 */
@RestController
@RequestMapping("/agreement")
public class AgreementController extends BaseController {

    @Autowired
    private AgreementService agreementService;

    /**
     * 查询协议设置表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<Agreement>> listByPage(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(agreementService.list()));
    }

    /**
     * 查询协议设置表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<Agreement> details(Long id) {
        return R.ok(agreementService.getById(id));
    }

    /**
     * 修改协议设置表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改协议设置表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody Agreement agreement) {
        return R.ok(agreementService.updateById(agreement));
    }

}

