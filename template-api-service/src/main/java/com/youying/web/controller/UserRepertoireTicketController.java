package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserRepertoireTicket;
import com.youying.common.core.page.PageDomain;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.service.UserRepertoireTicketService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 用户电子票
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@RestController
@RequestMapping("/user-repertoire-ticket")
public class UserRepertoireTicketController extends BaseController {
    @Autowired
    private UserRepertoireTicketService userRepertoireTicketService;

    /**
     * 查询用户电子票表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<UserRepertoireTicket>> findSysUserList(@RequestBody PageDomain pageDomain) {
        startPage(pageDomain);
        return R.ok(getTableList(userRepertoireTicketService.list()));
    }


    /**
     * 查询用户电子票表列表
     *
     * @return
     */
    @GetMapping(value = "/list")
    public R<List<UserRepertoireTicket>> findSysUserList() {
        return R.ok(userRepertoireTicketService.list());
    }

    /**
     * 查询用户电子票表详情
     *
     * @return
     */
    @GetMapping(value = "/details")
    public R<UserRepertoireTicket> details(Long id) {
        return R.ok(userRepertoireTicketService.getById(id));
    }

    /**
     * 添加用户电子票表
     *
     * @return
     */
    @PostMapping(value = "/add")
    @Log(title = "添加用户电子票表数据", businessType = BusinessType.INSERT)
    public R<?> add(@RequestBody UserRepertoireTicket sysUser) {
        return R.ok(userRepertoireTicketService.save(sysUser));
    }

    /**
     * 修改用户电子票表
     *
     * @return
     */
    @PutMapping(value = "/update")
    @Log(title = "修改用户电子票表数据", businessType = BusinessType.UPDATE)
    public R<?> update(@RequestBody UserRepertoireTicket sysUser) {
        return R.ok(userRepertoireTicketService.updateById(sysUser));
    }

    /**
     * 删除用户电子票表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除用户电子票表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(userRepertoireTicketService.removeBatchByIds(Arrays.asList(ids)));
        }
        return R.ok();
    }
}
