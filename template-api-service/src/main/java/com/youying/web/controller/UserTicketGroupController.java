package com.youying.web.controller;

import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.domain.entity.UserTicketGroup;
import com.youying.common.core.page.TableList;
import com.youying.system.domain.user.UserRequest;
import com.youying.system.service.UserTicketGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户电子票分组
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@RestController
@RequestMapping("/userTicketGroup")
public class UserTicketGroupController extends BaseController {

    @Autowired
    private UserTicketGroupService userTicketGroupService;

    /**
     * 查询用户电子票分组
     *
     * @return
     */
    @PostMapping(value = "/findUserTicketGroup")
    public R<TableList<UserTicketGroup>> findUserTicketGroup(@RequestBody UserRequest request) {
        startPage(request);
        return R.ok(getTableList(userTicketGroupService.findUserTicketGroup(request)));
    }
}

