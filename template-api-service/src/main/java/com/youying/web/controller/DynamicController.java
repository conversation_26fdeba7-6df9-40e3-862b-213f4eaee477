package com.youying.web.controller;

import com.youying.common.annotation.Log;
import com.youying.common.core.controller.BaseController;
import com.youying.common.core.domain.R;
import com.youying.common.core.page.TableList;
import com.youying.common.enums.BusinessType;
import com.youying.system.domain.dynamic.DynamicRequest;
import com.youying.system.domain.dynamic.DynamicResponse;
import com.youying.system.service.DynamicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * 剧目剧场动态表
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@RestController
@RequestMapping("/dynamic")
public class DynamicController extends BaseController {

    @Autowired
    private DynamicService dynamicService;

    /**
     * 查询剧目剧场动态表列表(分页)
     *
     * @return
     */
    @PostMapping(value = "/listByPage")
    public R<TableList<DynamicResponse>> listByPage(@RequestBody DynamicRequest request) {
        startPage(request);
        return R.ok(getTableList(dynamicService.listByPage(request)));
    }

    /**
     * 修改剧目剧场动态状态
     *
     * @return
     */
    @PutMapping(value = "/updateStatus/{id}")
    @Log(title = "修改剧目剧场动态状态", businessType = BusinessType.UPDATE)
    public R<?> updateStatus(@PathVariable("id") Long id) {
        return R.ok(dynamicService.updateStatus(id));
    }

    /**
     * 查询剧目剧场动态详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/details")
    public R<DynamicResponse> details(Long id) {
        return R.ok(dynamicService.details(id));
    }

    /**
     * 删除剧目剧场动态表
     *
     * @return
     */
    @DeleteMapping(value = "/delete/{ids}")
    @Log(title = "删除剧目剧场动态表数据", businessType = BusinessType.DELETE)
    public R<?> update(@PathVariable("ids") Long[] ids) {
        if (ids.length > 0) {
            return R.ok(dynamicService.delete(Arrays.asList(ids)));
        }
        return R.ok();
    }

}

