<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserReceivingRecordsMapper">

    <resultMap id="userReceivingMap" type="UserReceivingRecordsResponse">
        <id column="id" property="id"/>
        <result column="badge_type" property="badgeType"/>
        <result column="relation_id" property="relationId"/>
        <association property="rankMedalInfo" select="rankMedalInfoQuery" column="{badge_type=badge_type,relation_id=relation_id}" javaType="RankMedalInfo" />
        <collection property="actorInformationList" select="actorInformationQuery" column="id" ofType="string" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserReceivingRecords">
        <id column="id" property="id"/>
        <result column="no" property="no"/>
        <result column="send_id" property="sendId"/>
        <result column="user_id" property="userId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="badge_type" property="badgeType"/>
        <result column="seat_number" property="seatNumber"/>
        <result column="amount" property="amount"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `no`, send_id, user_id, theater_id, repertoire_id, badge_type, seat_number, amount
    </sql>

    <select id="actorInformationQuery" resultType="string">
        SELECT
            actor_information
        FROM
            t_user_receiving_records_text
        WHERE
            user_receiving_records_id = #{id}
    </select>

    <select id="rankMedalInfoQuery" resultType="RankMedalInfo">
        SELECT
            rmi.id,
            rmi.`name`,
            rmi.color,
            rmi.rank_medal_name
        FROM
            t_rank_medal_info AS rmi
        WHERE
            '4' = #{badge_type}
          AND rmi.id = #{relation_id}
    </select>

    <select id="listByPage" resultMap="userReceivingMap">
        SELECT
            urr.id,
            urr.user_id,
            urr.actor_information,
            u.`name` AS userName,
            u.phone,
            rm.`name` AS rankMedalName,
            rmi.`name` AS rankMedalLevel,
            rmi.color,
            urr.send_id,
            urr.collection_no,
            urr.relation_id,
            urr.upgrade_status,
            urr.upgrade_time,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            ( CASE urr.badge_type WHEN 3 THEN sb.cover_picture ELSE urr.image END ) AS image,
            urr.`time`,
            urr.amount,
            urr.badge_type,
            urr.seat_number,
            ( CASE urr.badge_type WHEN 3 THEN sb.issuer_name ELSE urr.issuer_name END ) AS issuer_name,
            sbr.look_number,
            mi.expense_price,
            urr.create_time,
            urr.file_url
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = urr.theater_id
            LEFT JOIN t_user AS u ON u.id = urr.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
            LEFT JOIN t_rank_medal AS rm ON rm.id = rmi.rank_medal_id
            LEFT JOIN t_souvenir_badge AS sb ON sb.id = urr.relation_id
            LEFT JOIN t_souvenir_badge_require AS sbr ON sbr.souvenir_badge_id = urr.relation_id
            LEFT JOIN t_rank_medal_info AS mi ON mi.id = urr.relation_id
        <where>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (u.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
            <if test="badgeType != null and badgeType != ''">
                AND FIND_IN_SET(urr.badge_type, #{badgeType})
            </if>
            <if test="repertoireId != null and repertoireId != ''">
                AND FIND_IN_SET(urr.repertoire_id, #{repertoireId})
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(urr.theater_id, #{theaterId})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(urr.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        ORDER BY
        urr.create_time DESC, urr.id
    </select>

    <select id="findCollectionRecipient" resultType="UserReceivingRecordsResponse">
        SELECT
            u.id,
            u.phone,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            rmi.rank_medal_name,
            rmi.`name` AS `rankMedalLevel`,
            rmi.color AS rankMedalColor ,
            urr.collection_no,
            urr.send_id,
            urr.create_time,
            urr.upgrade_status
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_user AS u ON u.id = urr.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
        <where>
            urr.badge_type = #{badgeType}
            <choose>
                <when test="badgeType == '4'.toString()">
                    AND urr.relation_id IN (SELECT id FROM t_rank_medal_info WHERE rank_medal_id = #{relationId})
                </when>
                <when test="badgeType == '3'.toString()">
                    AND urr.relation_id = #{relationId}
                </when>
                <otherwise>
                    AND urr.portfolio_id = #{relationId}
                </otherwise>
            </choose>
            <if test="upgradeStatus != null">
                AND urr.upgrade_status = #{upgradeStatus}
            </if>
        </where>
        ORDER BY urr.create_time DESC , urr.id
    </select>

    <select id="findUserReceivingRecordsAddCount" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM
            t_user_receiving_records AS urr
        <where>
            urr.badge_type = #{badgeType}
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(urr.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
    </select>

    <select id="export" resultType="com.youying.system.domain.userreceivingrecords.UserReceivingRecordsEx">
        SELECT
            urr.id,
            u.`name` AS userName,
            u.phone,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            urr.issuer_name,
            urr.badge_type,
            urr.upgrade_status,
            urr.upgrade_time,
            urr.time,
            urr.amount,
            urr.seat_number,
            IFNULL( rmi1.expense_price, 0 ) AS expensePrice,
            IFNULL( rmi1.expense_number, 0 ) AS expenseNumber,
            urr.create_time
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_theater AS t ON t.id = urr.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = urr.repertoire_id
            LEFT JOIN t_user AS u ON u.id = urr.user_id
            LEFT JOIN t_rank_medal_info AS rmi1 ON rmi1.id = urr.relation_id
            AND badge_type = 4
        <where>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (u.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
            <if test="badgeType != null and badgeType != ''">
                AND FIND_IN_SET(urr.badge_type, #{badgeType})
            </if>
            <if test="repertoireId != null and repertoireId != ''">
                AND FIND_IN_SET(urr.repertoire_id, #{repertoireId})
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(urr.theater_id, #{theaterId})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(urr.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        ORDER BY
            urr.create_time DESC, urr.id
    </select>

    <select id="findUserLookCount" resultType="com.youying.system.domain.userreceivingrecords.UserGetResponse">
        SELECT
            COUNT( 1 ) AS number,
            user_id,
            IFNULL( u.amount, 0 ) AS price
        FROM
            t_user_receiving_records AS urr
            LEFT JOIN t_user AS u ON u.id = urr.user_id
        <where>
            badge_type = '1'
            <if test="code == '2'.toString()">
                AND theater_id = #{relationId}
            </if>
            <if test="code == '1'.toString()">
                AND repertoire_id = #{relationId}
            </if>
        </where>
        GROUP BY
            user_id
    </select>

    <select id="findUserLookAssignRepertoire" resultType="java.lang.Long">
        SELECT
            COUNT( 1 )
        FROM
            t_user_receiving_records AS urr
        <where>
            urr.badge_type = '1'
            AND urr.user_id = #{userId}
            <if test="repertoireInfoId != '0'.toString()">
                AND urr.repertoire_info_detail_id = #{repertoireInfoId}
                AND urr.theater_id = #{theaterId}
            </if>
            <if test="lookNumber != '0'.toString() and startTime != null and endTime != null">
                AND
                (
                SELECT
                    COUNT( 1 )
                FROM
                    t_user_receiving_records
                WHERE
                    user_id = #{userId}
                    AND badge_type = '1'
                    AND theater_id = #{theaterId}
                    AND DATE_FORMAT(urr.time,'%Y-%m-%e %H:%i:00') BETWEEN #{startTime} AND #{endTime}
                ) >= #{lookNumber}
            </if>
            <if test="rankMedalId != '0'.toString() and rankMedalInfoId != '0'.toString()">
                AND
                (
                SELECT
                    COUNT( 1 )
                FROM
                    t_user_push_collection AS upc
                    LEFT JOIN t_rank_medal_info AS rm ON rm.id = upc.rank_medal_info_id
                WHERE
                    upc.user_id = #{userId}
                    AND upc.rank_medal_id = #{rankMedalId}
                    AND upc.status = '1'
                    AND rm.`name` > (SELECT `name` FROM t_rank_medal_info WHERE id = #{rankMedalInfoId})
                ) > 0
            </if>
        </where>
    </select>

</mapper>
