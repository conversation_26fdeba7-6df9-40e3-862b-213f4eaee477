<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.MerchantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Merchant">
        <id column="id" property="id"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="merchant_category" property="merchantCategory"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="phone" property="phone"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="address" property="address"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="business_license _front" property="businessLicenseFront"/>
        <result column="business_license _reverse" property="businessLicenseReverse"/>
        <result column="remark" property="remark"/>
        <result column="deleted" property="deleted"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_name, merchant_category, contact_person, phone, `account`, `password`, address, start_time,
        end_time, business_license _front, business_license _reverse, remark, deleted, `status`, create_by, create_time,
        update_by, update_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.merchant.MerchantResponse">
        SELECT
            m.id,
            m.merchant_user_id,
            m.`merchant_name`,
            m.merchant_category,
            m.contact_person,
            m.phone,
            m.address,
            m.remark,
            m.create_time
        FROM
            t_merchant AS m
        <where>
            m.deleted = '1'
            <if test="merchantCategory != null and merchantCategory != ''">
                AND FIND_IN_SET(m.merchant_category, #{merchantCategory})
            </if>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (m.merchant_name LIKE CONCAT('%',#{keyword},'%'))
                OR
                (m.phone LIKE CONCAT('%',#{keyword},'%'))
                OR
                (m.remark LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(m.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        ORDER BY
            m.create_time DESC
    </select>

    <select id="pull" resultType="com.youying.common.core.common.PullResponse">
        SELECT
            m.id,
            m.`merchant_name` AS `name`
        FROM
            t_merchant AS m
        <where>
            m.deleted = 1
            <if test="merchantCategory != null and merchantCategory != ''">
                AND FIND_IN_SET(merchant_category, #{merchantCategory})
            </if>
            <if test="status != null">
                AND r.`status` = #{status}
            </if>
        </where>
        ORDER BY
            m.create_time DESC,
            m.id
    </select>

    <select id="findMerchantAddCount" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM
        t_merchant AS m
        <where>
            m.deleted = 1
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(m.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
    </select>

</mapper>
