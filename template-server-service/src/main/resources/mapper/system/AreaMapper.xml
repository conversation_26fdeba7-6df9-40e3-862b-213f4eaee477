<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.AreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Area">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="parent_id" property="parentId"/>
        <result column="shortname" property="shortname"/>
        <result column="level_type" property="levelType"/>
        <result column="city_code" property="cityCode"/>
        <result column="zipcode" property="zipcode"/>
        <result column="lng" property="lng"/>
        <result column="lat" property="lat"/>
        <result column="pinyin" property="pinyin"/>
        <result column="status" property="status"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `name`, `code`, parent_id, shortname, level_type, city_code, zipcode, lng, lat, pinyin, `status`, sort,
        remark
    </sql>

</mapper>
