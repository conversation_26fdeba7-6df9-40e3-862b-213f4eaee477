<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.CommentMapper">

    <resultMap id="commentMap" type="CommentResponse">
        <id column="id" property="id"/>
        <collection property="commentInfoResponseList" select="commonInfoQuery" column="id" ofType="commentInfoResponse" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Comment">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="repertoire_info_detail_id" property="repertoireInfoDetailId"/>
        <result column="user_id" property="userId"/>
        <result column="reply_id" property="replyId"/>
        <result column="parent_id" property="parentId"/>
        <result column="content" property="content"/>
        <result column="comment_time" property="commentTime"/>
        <result column="grade" property="grade"/>
        <result column="visible" property="visible"/>
        <result column="top" property="top"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, theater_id, repertoire_id, repertoire_info_detail_id, user_id, reply_id, parent_id, content,
        comment_time, grade, well, bad, visible, top, `status`, create_by, create_time, update_by, update_time
    </sql>

    <select id="commonInfoQuery" resultType="commentInfoResponse">
        SELECT
            c.id,
            u.`name` AS userName,
            c.user_merchant_id,
            c.create_time,
            c.content,
            c.repertoire_reply_status,
            c.theater_reply_status,
            ( CASE c.user_merchant_id WHEN 0 THEN u1.`name` ELSE mu.`name` END ) AS replyName,
            ( CASE c.user_merchant_id WHEN 0 THEN u1.`avatar` ELSE mu.`avatar` END ) AS replyAvatar,
            SUM( CASE WHEN ci.type = 1 THEN 1 ELSE 0 END ) AS likeCount,
            SUM( CASE WHEN ci.type = 0 THEN 1 ELSE 0 END ) AS dislikeCount
        FROM
            t_comment AS c
            LEFT JOIN t_user AS u ON u.id = c.user_id
            LEFT JOIN t_user AS u1 ON u1.id = c.reply_id
            LEFT JOIN t_merchant_user AS mu ON mu.id = c.user_merchant_id
            LEFT JOIN t_comment_info AS ci ON ci.comment_id = c.id
        WHERE
            c.parent_id = #{id}
        GROUP BY
            c.id
    </select>

    <select id="listByPage" resultType="com.youying.system.domain.comment.CommentResponse">
        SELECT
            c.id,
            u.`name` AS userName,
            u.`avatar` AS userAvatar,
            u.phone,
            c.user_id,
            rmi.rank_medal_name,
            rmi.`name` AS `rankMedalLevel`,
            rmi.color AS rankMedalColor,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            c.content,
            c.user_merchant_id,
            COUNT( DISTINCT c1.id ) AS replyCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 1 ) AS likeCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 0 ) AS dislikeCount,
            c.create_time,
            rid.start_time,
            rid.end_time,
            c.`status`,
            c.deleted,
            c.top,
            u.speak_status,
            c.theater_content
        FROM
            t_comment AS c
            LEFT JOIN t_user AS u ON u.id = c.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
            LEFT JOIN t_theater AS t ON t.id = c.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = c.repertoire_id
            LEFT JOIN t_repertoire_info_detail AS rid ON rid.id = c.repertoire_info_detail_id
            LEFT JOIN t_comment AS c1 ON c1.parent_id = c.id
        <where>
            <if test="parentId == null">
                AND c.parent_id = 0
            </if>
            <if test="parentId != null">
                AND c.parent_id = #{parentId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (c.`content` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (c.`theater_content` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (u.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (u.`phone` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (rmi.rank_medal_name LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(c.theater_id, #{theaterId})
            </if>
            <if test="repertoireId != null and repertoireId != ''">
                AND FIND_IN_SET(c.repertoire_id, #{repertoireId})
            </if>
            <if test="status != null and status != ''">
                AND FIND_IN_SET(c.`status`, #{status})
            </if>
            <if test="top != null and top != ''">
                AND FIND_IN_SET(c.top, #{top})
            </if>
            <if test="deleted != null and deleted != ''">
                AND FIND_IN_SET(c.deleted, #{deleted})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(c.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        GROUP BY
            c.id
        ORDER BY
            c.create_time DESC , c.id
    </select>

    <select id="details" resultMap="commentMap">
        SELECT
            c.id,
            u.`name` AS userName,
            u.`avatar` AS userAvatar,
            rmi.rank_medal_name,
            rmi.`name` AS `rankMedalLevel`,
            rmi.color AS rankMedalColor,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            r.cover_picture AS repertoireCoverPicture,
            t.cover_picture AS theaterCoverPicture,
            c.content,
            c.theater_content,
            COUNT( DISTINCT c1.id ) AS replyCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 1 ) AS likeCount,
            ( SELECT COUNT(1) FROM t_comment_info WHERE comment_id = c.id AND `type` = 0 ) AS dislikeCount,
            c.create_time,
            rid.start_time,
            rid.end_time
        FROM
            t_comment AS c
            LEFT JOIN t_user AS u ON u.id = c.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
            LEFT JOIN t_theater AS t ON t.id = c.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = c.repertoire_id
            LEFT JOIN t_repertoire_info_detail AS rid ON rid.id = c.repertoire_info_detail_id
            LEFT JOIN t_comment AS c1 ON c1.parent_id = c.id
        WHERE
            c.id = #{id}
        GROUP BY
            c.id
    </select>

</mapper>
