<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.IssueMapper">

    <resultMap id="issueMap" type="IssueResponse">
        <id column="id" property="id"/>
        <collection property="userList" select="userIssueQuery" column="id" ofType="IssueResponse" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Issue">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="repertoire_info_detail_id" property="repertoireInfoDetailId"/>
        <result column="user_id" property="userId"/>
        <result column="reply_id" property="replyId"/>
        <result column="parent_id" property="parentId"/>
        <result column="content" property="content"/>
        <result column="top" property="top"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, theater_id, repertoire_id, repertoire_info_detail_id, user_id, reply_id, parent_id, content,
        top, `status`, create_by, create_time
    </sql>

    <select id="userIssueQuery" resultType="IssueResponse">
        SELECT
            i.id,
            mu.`name` AS merchantReplyName,
            mu.`avatar` AS merchantReplyAvatar,
            u.`name` AS replyName,
            u.`avatar` AS replyAvatar,
            i.user_merchant_id,
            i.content,
            i.create_time
        FROM
            t_issue AS i
            LEFT JOIN t_merchant_user AS mu ON mu.id = i.user_merchant_id
            LEFT JOIN t_user AS u ON u.id = i.reply_id
        WHERE
            i.parent_id = #{id}
        ORDER BY
            i.create_time
    </select>

    <select id="listByPage" resultType="com.youying.system.domain.issue.IssueResponse">
        SELECT
            i.id,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            rmi.rank_medal_name,
            rmi.`name` AS `rankMedalLevel`,
            rmi.color AS rankMedalColor,
            i.create_time,
            i.`status`,
            i.content,
            COUNT( DISTINCT i1.user_id ) replyCount
        FROM
            t_issue AS i
            LEFT JOIN t_repertoire AS r ON r.id = i.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = i.theater_id
            LEFT JOIN t_user AS u ON u.id = i.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
            LEFT JOIN t_issue AS i1 ON i1.parent_id = i.id
        <where>
            i.parent_id = 0
            <choose>
                <when test="merchantCategory == '1'.toString()">
                    AND i.repertoire_id > 0
                </when>
                <when test="merchantCategory == '2'.toString()">
                    AND i.theater_id > 0
                </when>
                <otherwise>
                    AND 1 = 0
                </otherwise>
            </choose>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (i.content LIKE CONCAT('%',#{keyword},'%'))
                OR
                (u.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
            <if test="repertoireId != null and repertoireId != ''">
                AND FIND_IN_SET(i.repertoire_id, #{repertoireId})
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(i.theater_id, #{theaterId})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(i.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        GROUP BY
            i.id
        ORDER BY i.create_time DESC
    </select>

    <select id="details" resultMap="issueMap">
        SELECT
            i.id,
            r.`name` AS repertoireName,
            t.`name` AS theaterName,
            u.`name` AS userName,
            u.avatar AS userAvatar,
            r.cover_picture AS repertoireCoverPicture,
            t.cover_picture AS theaterCoverPicture,
            i.create_time,
            i.content
        FROM
            t_issue AS i
            LEFT JOIN t_repertoire AS r ON r.id = i.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = i.theater_id
            LEFT JOIN t_user AS u ON u.id = i.user_id
        WHERE
            i.id = #{id}
    </select>

</mapper>
