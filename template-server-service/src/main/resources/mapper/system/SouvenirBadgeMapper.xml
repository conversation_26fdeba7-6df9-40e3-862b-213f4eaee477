<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.SouvenirBadgeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.SouvenirBadge">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="name" property="name"/>
        <result column="theater_id" property="theaterId"/>
        <result column="model" property="model"/>
        <result column="issuer_name" property="issuerName"/>
        <result column="issued_quantity" property="issuedQuantity"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="introduction" property="introduction"/>
        <result column="sms_notify" property="smsNotify"/>
        <result column="status" property="status"/>
        <result column="audit" property="audit"/>
        <result column="reasons_rejection" property="reasonsRejection"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="souvenirBadgeResponseMap" type="SouvenirBadgeResponse">
        <id column="id" property="id"/>
        <association property="souvenirBadgeRequire" select="findSouvenirBadgeRequire" column="id" javaType="souvenirBadgeRequireResponse" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, `name`, theater_id, model, issuer_name, issued_quantity, start_time, end_time, introduction,
        sms_notify, `status`, audit, reasons_rejection, create_by, create_time, update_by, update_time
    </sql>

    <select id="findSouvenirBadgeRequire" resultType="souvenirBadgeRequireResponse">
        SELECT
            sbr.*,
            rmi.rank_medal_name,
            rmi.`name` AS rankMedalLevel,
            rid.start_time AS repertoireStartTime,
            rid.end_time  AS repertoireEndTime,
            ( SELECT GROUP_CONCAT(`name`) FROM t_user WHERE FIND_IN_SET(id,sbr.white_list) ) AS whiteNameList
        FROM
            t_souvenir_badge_require AS sbr
            LEFT JOIN t_rank_medal_info as rmi on rmi.id = sbr.rank_medal_info_id
            LEFT JOIN t_repertoire_info_detail AS rid on rid.id = sbr.repertoire_info_detail_id
        WHERE
            souvenir_badge_id = #{id}
    </select>

    <select id="listByPage" resultMap="souvenirBadgeResponseMap">
        SELECT
            sb.id,
            sb.`name`,
            sb.issuer_name,
            sb.model,
            sb.issued_quantity,
            sb.start_time,
            sb.end_time,
            sb.`status`,
            sb.sold_out,
            sb.look_status,
            ( SELECT COUNT( 1 ) FROM t_user_receiving_records WHERE relation_id = sb.id AND badge_type = 3 ) AS receivedNumber,
            sb.audit,
            sb.reasons_rejection,
            t.`name` AS theaterName,
            sb.cover_picture AS coverPicture,
            m.merchant_name
        FROM
            t_souvenir_badge AS sb
            LEFT JOIN t_theater AS t ON t.id = sb.theater_id
            LEFT JOIN t_merchant AS m ON m.id = sb.merchant_id
        <where>
            <if test="merchantId != null and merchantId != ''">
                AND FIND_IN_SET(sb.merchant_id, #{merchantId})
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(sb.theater_id, #{theaterId})
            </if>
            <if test="audit != null and audit != ''">
                AND FIND_IN_SET(sb.audit, #{audit})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(sb.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (sb.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (m.merchant_name LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
        </where>
        ORDER BY
            sb.create_time DESC
    </select>

</mapper>
