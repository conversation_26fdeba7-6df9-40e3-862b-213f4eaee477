<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.TicketGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.TicketGroup">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="user_id" property="userId"/>
        <result column="status" property="status"/>
        <result column="sort" property="sort"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , `name`, `type`, user_id, `status`, sort, create_by, create_time, update_by, update_time
    </sql>

    <select id="listByPage" resultType="com.youying.common.core.domain.entity.TicketGroup">
        SELECT
            *
        FROM
            t_ticket_group
        <where>
            type = 0
            <if test="keyword != null and keyword != ''">
                AND `name` LIKE CONCAT('%',#{keyword},'%')
            </if>
        </where>
        ORDER BY sort , create_time DESC , id
    </select>

</mapper>
