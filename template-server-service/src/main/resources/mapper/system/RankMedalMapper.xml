<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.RankMedalMapper">

    <resultMap id="detailsMap" type="RankMedalResponse">
        <id column="id" property="id"/>
        <collection property="rankMedalInfoList" select="rankMedalInfoQuery" column="id" ofType="rankMedalInfo" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.RankMedal">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="audit" property="audit"/>
        <result column="audit_pass_time" property="auditPassTime"/>
        <result column="reasons_rejection" property="reasonsRejection"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `name`, merchant_id, theater_id, repertoire_id, audit, audit_pass_time, reasons_rejection, deleted,
        `status`, create_by, create_time, update_by, update_time
    </sql>

    <select id="rankMedalInfoQuery" resultType="rankMedalInfo" >
        SELECT
            rmi.id,
            rmi.rank_medal_name,
            rmi.`name`,
            rmi.expense_price,
            rmi.expense_number,
            rmi.color
        FROM
            t_rank_medal_info AS rmi
        WHERE
            rmi.rank_medal_id = #{id}
    </select>

    <select id="listByPage" resultType="com.youying.system.domain.rankmedal.RankMedalResponse">
        SELECT
            rm.id,
            rm.`name`,
            rm.theater_id,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            m.merchant_name,
            m.merchant_category,
            GROUP_CONCAT( DISTINCT rmi.`name` ) AS rankMedalLevel,
            COUNT( DISTINCT urr.id ) AS receivedNumber,
            rm.audit,
            rm.reasons_rejection,
            rm.create_time
        FROM
            t_rank_medal AS rm
            LEFT JOIN t_theater AS t ON t.id = rm.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = rm.repertoire_id
            LEFT JOIN t_merchant AS m ON m.id = rm.merchant_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.rank_medal_id = rm.id
            LEFT JOIN t_user_receiving_records AS urr ON urr.relation_id = rmi.id
            AND badge_type = 4
        <where>
            <if test="merchantId != null and merchantId != ''">
                AND FIND_IN_SET(rm.merchant_id, #{merchantId})
            </if>
            <if test="merchantCategory != null and merchantCategory != ''">
                AND FIND_IN_SET(m.merchant_category, #{merchantCategory})
            </if>
            <if test="repertoireId != null and repertoireId != ''">
                AND FIND_IN_SET(rm.repertoire_id, #{repertoireId})
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(rm.theater_id, #{theaterId})
            </if>
            <if test="audit != null and audit != ''">
                AND FIND_IN_SET(rm.audit, #{audit})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(rm.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (rm.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (m.merchant_name LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
        </where>
        GROUP BY
            rm.id
        ORDER BY
            FIELD(rm.audit, 0,2,1) , rm.create_time DESC
    </select>

    <select id="details" resultMap="detailsMap">
        SELECT
            rm.id,
            rm.`name`,
            rm.theater_id,
            rm.repertoire_id,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            m.merchant_name,
            m.merchant_category ,
            rm.audit,
            rm.create_time
        FROM
            t_rank_medal AS rm
            LEFT JOIN t_theater AS t ON t.id = rm.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = rm.repertoire_id
            LEFT JOIN t_merchant AS m ON m.id = rm.merchant_id
        WHERE
            rm.id = #{id}
    </select>

</mapper>
