<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.AdvertisingPictureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.AdvertisingPicture">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="image" property="image"/>
        <result column="url" property="url"/>
        <result column="status" property="status"/>
        <result column="sort" property="sort"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, image, url, `status`, sort, create_by, create_time, update_by, update_time
    </sql>

    <select id="listByPage" resultType="com.youying.common.core.domain.entity.AdvertisingPicture">
        SELECT
            ap.id,
            ap.image,
            ap.title,
            ap.`status`,
            ap.create_time
        FROM
            t_advertising_picture AS ap
        <where>
            <if test="status != null and status != ''">
                AND FIND_IN_SET(ap.status, #{status})
            </if>
            <if test="keyword != null and keyword != ''">
                AND ap.`title` LIKE CONCAT('%',#{keyword},'%')
            </if>
        </where>
        ORDER BY
            ap.sort,
            ap.create_time
    </select>

</mapper>
