<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.AutoReplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.AutoReply">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="body" property="body"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, theater_id, repertoire_id, body, create_by, create_time, update_by, update_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.autoreply.AutoReplyResponse">
        SELECT
            ar.id,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            ar.body,
            ar.create_time
        FROM
            t_auto_reply AS ar
            LEFT JOIN t_theater AS t ON t.id = ar.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = ar.repertoire_id
        <where>
            <choose>
                <when test="merchantCategory == '1'.toString()">
                    AND ar.repertoire_id > 0
                </when>
                <when test="merchantCategory == '2'.toString()">
                    AND ar.theater_id > 0
                </when>
                <otherwise>
                    AND 1 = 0
                </otherwise>
            </choose>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (ar.body LIKE CONCAT('%',#{keyword},'%'))
                OR
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
            <if test="repertoireId != null and repertoireId != ''">
                AND FIND_IN_SET(ar.repertoire_id, #{repertoireId})
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(ar.theater_id, #{theaterId})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(ar.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        ORDER BY
            ar.create_time DESC,
            ar.id
    </select>

</mapper>
