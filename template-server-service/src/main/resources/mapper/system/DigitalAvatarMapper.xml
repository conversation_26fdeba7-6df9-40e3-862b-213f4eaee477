<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.DigitalAvatarMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.DigitalAvatar">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="issuer_name" property="issuerName"/>
        <result column="issued_quantity" property="issuedQuantity"/>
        <result column="audit" property="audit"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="digitalAvatarMap" type="com.youying.system.domain.digitalavatar.DigitalAvatarResponse">
        <id column="id" property="id"/>
        <collection property="digitalAvatarImageList" select="findDigitalAvatarImage" column="id" ofType="com.youying.common.core.domain.entity.DigitalAvatarImage">
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, theater_id, repertoire_id, start_time, end_time, issuer_name, issued_quantity, audit,
        create_by, create_time, update_by, update_time
    </sql>

    <select id="findDigitalAvatarImage" resultType="com.youying.common.core.domain.entity.DigitalAvatarImage">
        SELECT
            `group`,
            sort,
            image
        FROM
            t_digital_avatar_image
        WHERE
            digital_avatar_id = #{id}
    </select>

    <select id="listByPage" resultMap="digitalAvatarMap">
        SELECT
            da.id,
            da.batch,
            da.theater_id,
            da.repertoire_id,
            da.start_time,
            da.end_time,
            da.issuer_name,
            da.issued_quantity,
            da.audit,
            da.reasons_rejection,
            da.sold_out,
            da.`status`,
            da.create_time,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            m.merchant_name,
            ( SELECT COUNT( 1 ) FROM t_user_receiving_records WHERE badge_type = '2' AND relation_id = da.id ) AS getCount
        FROM
            t_digital_avatar AS da
            LEFT JOIN t_theater AS t ON t.id = da.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = da.repertoire_id
            LEFT JOIN t_merchant AS m ON m.id = da.merchant_id
        <where>
            <if test="merchantId != null and merchantId != ''">
                AND FIND_IN_SET(da.merchant_id, #{merchantId})
            </if>
            <if test="repertoireId != null and repertoireId != ''">
                AND FIND_IN_SET(da.repertoire_id, #{repertoireId})
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(da.theater_id, #{theaterId})
            </if>
            <if test="audit != null and audit != ''">
                AND FIND_IN_SET(da.audit, #{audit})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(da.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (m.merchant_name LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
        </where>
        ORDER BY
            da.create_time DESC
    </select>

</mapper>
