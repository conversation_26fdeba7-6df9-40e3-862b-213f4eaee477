package com.youying.system.domain.comment;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-06
 */
@Data
public class CommentResponse {
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 剧场ID (0为暂无)
     */
    private Long theaterId;

    /**
     * 剧目ID (0为暂无)
     */
    private Long repertoireId;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 等级勋章名称
     */
    private String rankMedalName;

    /**
     * 等级勋章名称等级
     */
    private String rankMedalLevel;

    /**
     * 等级勋章颜色
     */
    private String rankMedalColor;

    /**
     * 内容
     */
    private String content;

    /**
     * 剧场内容
     */
    private String theaterContent;

    /**
     * 回复条数
     */
    private Integer replyCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 点踩数
     */
    private Integer dislikeCount;

    /**
     * 回复状态
     */
    private Integer status;

    /**
     * 删除状态
     */
    private Integer deleted;

    /**
     * 用户评论状态
     */
    private Integer speakStatus;

    /**
     * 评分
     */
    private Integer grade;

    /**
     * 置顶
     */
    private Integer top;

    /**
     * 剧场封面图
     */
    private String theaterCoverPicture;

    /**
     * 剧目封面图
     */
    private String repertoireCoverPicture;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 回复记录
     */
    private List<CommentInfoResponse> commentInfoResponseList = new ArrayList<>();

    public void setContent(String content) {
        this.content = content.replaceAll("<[^>]*>", "");
    }

    public void setTheaterContent(String theaterContent) {
        this.theaterContent = theaterContent.replaceAll("<[^>]*>", "");
    }
}
