package com.youying.system.domain.repertoireinfo;

import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.page.PageDomain;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
public class RepertoireInfoRequest extends PageDomain {
    /**
     * 剧场ID
     */
    private String theaterId;

    /**
     * 商家ID
     */
    private String merchantId;

    /**
     * 剧目ID
     */
    private String repertoireId;

    /**
     * 状态
     */
    private String status;

    /**
     * 时间
     */
    private TimeRequest time = new TimeRequest();

    /**
     * 演出时间
     */
    private TimeRequest showTime = new TimeRequest();
}
