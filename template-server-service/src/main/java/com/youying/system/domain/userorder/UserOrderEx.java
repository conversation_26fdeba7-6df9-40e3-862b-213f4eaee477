package com.youying.system.domain.userorder;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
public class UserOrderEx {
    @Excel(name = "订单编号")
    private String orderNo;

    @Excel(name = "姓名")
    private String userName;

    @Excel(name = "手机号码")
    private String phone;

    @Excel(name = "剧目名称")
    private String repertoireName;

    @Excel(name = "剧场名称")
    private String theaterName;

    @Excel(name = "表演场次")
    private String time;

    @Excel(name = "票价")
    private String amount;

    @Excel(name = "订单金额")
    private BigDecimal payPrice;

    @Excel(name = "订单时间", format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "订单状态", replace = {"待支付_0", "已支付_1", "已取消_2"}, addressList = true)
    private Integer payStatus;
}
