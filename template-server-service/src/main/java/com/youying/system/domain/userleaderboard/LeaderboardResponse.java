package com.youying.system.domain.userleaderboard;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-01-09
 */
@Data
public class LeaderboardResponse {
    private Long id;

    /**
     * 剧目名称
     */
    private String name;

    /**
     * 排行榜统计
     */
    private Integer leaderboardCount;

    /**
     * 分组统计
     */
    private Integer ticketGroupCount;

    /**
     * 榜单数据
     */
    private List<LeaderboardVO> leaderboardList = new ArrayList<LeaderboardVO>();

    /**
     * 分组
     */
    private List<LeaderboardVO> ticketGroupList = new ArrayList<LeaderboardVO>();
}

@Data
class LeaderboardVO {
    private Long id;

    /**
     * 榜单名称
     */
    private String name;

    /**
     * 数量
     */
    private Integer count;
}
