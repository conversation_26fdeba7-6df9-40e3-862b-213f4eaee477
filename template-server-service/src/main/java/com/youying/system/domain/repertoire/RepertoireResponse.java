package com.youying.system.domain.repertoire;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
public class RepertoireResponse {
    private Long id;

    /**
     * 剧场名称
     */
    private String name;

    /**
     * 短标题
     */
    private String shortName;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 省
     */
    private Long provId;

    /**
     * 市
     */
    private Long cityId;

    /**
     * 区
     */
    private Long areaId;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 封面图URL
     */
    private String coverPicture;

    /**
     * 剧目图片,拼接
     */
    private String pictures;

    /**
     * 评分
     */
    private Double rating;

    /**
     * 好评率
     */
    private Double goodRatingRate;

    /**
     * 关注数量
     */
    private Integer focusNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 推荐（0否，1是）
     */
    private Integer recommend;

    /**
     * 简介
     */
    private String introduction;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    private Integer audit;

    /**
     * 审核通过时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditPassTime;

    /**
     * 标签
     */
    private String repertoireLabel;

    /**
     * 驳回原因
     */
    private String reasonsRejection;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 评论数量
     */
    private Integer commentCount;

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * 互动数量
     */
    private Integer interactionCount;

    /**
     * 勋章名称
     */
    private String rankMedalName;

    /**
     * 商家
     */
    private String merchantName;

    /**
     * 商家类型
     */
    private Integer merchantCategory;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
