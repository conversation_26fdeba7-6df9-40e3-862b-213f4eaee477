package com.youying.system.domain.repertoireinfodetail;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
public class RepertoireInfoDetailResponse {
    /**
     * id
     */
    private Long id;

    /**
     * 剧场名称
     */
    private String theaterName;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
