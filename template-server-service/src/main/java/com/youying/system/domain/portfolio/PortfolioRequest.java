package com.youying.system.domain.portfolio;

import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.page.PageDomain;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
public class PortfolioRequest extends PageDomain {
    /**
     * 剧场ID
     */
    private String theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 商家ID
     */
    private String merchantId;

    /**
     * 审核状态
     */
    private String audit;

    /**
     * 发放时间
     */
    private TimeRequest time = new TimeRequest();

    /**
     * 票类型
     */
    private String ticketType;
}
