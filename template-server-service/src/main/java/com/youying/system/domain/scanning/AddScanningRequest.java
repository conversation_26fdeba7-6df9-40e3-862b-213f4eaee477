package com.youying.system.domain.scanning;

import com.youying.common.core.domain.entity.ScanningInfo;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-10-24
 */
@Data
public class AddScanningRequest {
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 启用状态
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 规则详情
     */
    @NotEmpty(message = "规则不可以为空")
    private List<ScanningInfo> scanningInfoList = new ArrayList<ScanningInfo>();
}
