package com.youying.system.domain.usertreasure;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-10-11
 */
@Data
public class UserTreasureEx {
    @Excel(name = "用户名称")
    private String name;

    @Excel(name = "用户手机")
    private String phone;

    @Excel(name = "关注时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;

    @Excel(name = "是否观影")
    private String lookCount;
}
