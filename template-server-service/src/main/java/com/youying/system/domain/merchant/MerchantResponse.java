package com.youying.system.domain.merchant;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
public class MerchantResponse {
    private Long id;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 商家LOGO
     */
    private String logo;

    /**
     * 商家类别（1剧目，2剧场）
     */
    private Integer merchantCategory;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 账号
     */
    private String account;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 合作开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 合作结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 营业执照正面
     */
    private String businessLicenseFront;

    /**
     * 营业执照反面
     */
    private String businessLicenseReverse;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
