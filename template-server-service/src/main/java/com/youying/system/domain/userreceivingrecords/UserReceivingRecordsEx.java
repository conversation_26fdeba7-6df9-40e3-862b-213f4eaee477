package com.youying.system.domain.userreceivingrecords;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-07-03
 */
@Data
public class UserReceivingRecordsEx {

    @Excel(name = "用户名称")
    private String userName;

    @Excel(name = "用户电话")
    private String phone;

    @Excel(name = "关联剧场")
    private String theaterName;

    @Excel(name = "关联剧目")
    private String repertoireName;

    @Excel(name = "类型", replace = {"电子票_1", "数字头像_2", "纪念徽章_3", "等级勋章_4"}, addressList = true)
    private String badgeType;

    @Excel(name = "表演场次")
    private String time;

    @Excel(name = "座位号")
    private String seatNumber;

    @Excel(name = "票价")
    private String amount;

    @Excel(name = "≥满足条件（消费金额）")
    private String expensePrice;

    @Excel(name = "≥满足条件（观看场次数）")
    private String expenseNumber;

    @Excel(name = "领取时间", format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "是否升级", replace = {"未升级_0", "已升级_1"}, addressList = true)
    private Integer upgradeStatus;

    @Excel(name = "升级时间", format = "yyyy-MM-dd HH:mm:ss")
    private Date upgradeTime;
}
