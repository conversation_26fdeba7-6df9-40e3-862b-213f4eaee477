package com.youying.system.domain.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.system.domain.user.UserResponse;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
public class MessageResponse {
    private Long id;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 群发内容
     */
    private String body;

    /**
     * 短信通知
     */
    private String smsNotify;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 用户
     */
    private List<UserResponse> userList = new ArrayList<UserResponse>();
}
