package com.youying.system.domain.comment;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-06-06
 */
@Data
public class CommentInfoResponse {
    private Long id;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 内容
     */
    private String content;

    /**
     * 回复人
     */
    private String replyName;

    /**
     * 回复人头像
     */
    private String replyAvatar;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 点踩数
     */
    private Integer dislikeCount;

    /**
     * 官方回复
     */
    private Long userMerchantId;

    /**
     * 剧目商家回复状态
     */
    private Integer repertoireReplyStatus;

    /**
     * 剧场商家回复状态
     */
    private Integer theaterReplyStatus;
}
