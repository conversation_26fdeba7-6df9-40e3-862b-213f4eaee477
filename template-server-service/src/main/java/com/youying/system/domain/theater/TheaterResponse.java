package com.youying.system.domain.theater;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
public class TheaterResponse {
    private Long id;

    /**
     * 剧场名称
     */
    private String name;

    /**
     * 短标题
     */
    private String shortName;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 省
     */
    private Long provId;

    /**
     * 市
     */
    private Long cityId;

    /**
     * 区
     */
    private Long areaId;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 封面图URL
     */
    private String coverPicture;

    /**
     * 剧目图片,拼接
     */
    private String pictures;

    /**
     * 好评率
     */
    private Double goodRatingRate;

    /**
     * 关注数量
     */
    private Integer focusNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 推荐（0否，1是）
     */
    private Integer recommend;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    private Integer audit;

    /**
     * 审核通过时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditPassTime;

    /**
     * 驳回原因
     */
    private String reasonsRejection;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * 评论数量
     */
    private Integer commentCount;

    /**
     * 互动数量
     */
    private Integer interactionCount;
}
