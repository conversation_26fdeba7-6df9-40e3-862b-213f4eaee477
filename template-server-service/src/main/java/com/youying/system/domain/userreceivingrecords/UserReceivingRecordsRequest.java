package com.youying.system.domain.userreceivingrecords;

import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.page.PageDomain;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-06-01
 */
@Data
public class UserReceivingRecordsRequest extends PageDomain {
    /**
     * ID
     */
    private Long id;

    /**
     * ID
     */
    private Long relationId;

    /**
     * 1：电子票、2：数字头像、3：纪念徽章、4：等级勋章
     */
    private String badgeType;

    /**
     * 剧场ID
     */
    private String theaterId;

    /**
     * 剧目ID
     */
    private String repertoireId;

    /**
     * 升级状态
     */
    private Integer upgradeStatus;

    /**
     * 创建时间
     */
    private TimeRequest time = new TimeRequest();
}
