package com.youying.system.domain.usertreasure;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-06-25
 */
@Data
public class UserTreasureResponse {
    /**
     * 用户名称
     */
    private String name;

    /**
     * 用户手机
     */
    private String phone;

    /**
     * 等级勋章名称
     */
    private String rankMedalName;

    /**
     * 等级勋章名称等级
     */
    private String rankMedalLevel;

    /**
     * 等级勋章颜色
     */
    private String rankMedalColor;

    /**
     * 剧场数量
     */
    private Long theaterCount;

    /**
     * 是否观影 >0 ? 是:否
     */
    private Long lookCount;

    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date time;

    /**
     * 关注时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
