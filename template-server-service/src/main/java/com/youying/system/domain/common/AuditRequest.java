package com.youying.system.domain.common;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
public class AuditRequest {
    /**
     * id
     */
    private Long id;

    /**
     * 审核状态
     */
    private Integer audit;

    /**
     * 驳回原因
     */
    private String reasonsRejection;

    /**
     * AuditRequest
     * 免责声明
     */
    private String statement;

    /**
     * 金额
     */
    private BigDecimal price;

    /**
     * 电子票合成图
     */
    private String repertoireTicketUrl;

    /**
     * 短标题
     */
    private String shortName;

    /**
     * ocr编号
     */
    private String ocrNo;

    /**
     * 免费发放
     */
    private Integer free;

    /**
     * 数字头像合成图
     */
    private List<String> digitalAvatarUrl = new ArrayList<>();
}
