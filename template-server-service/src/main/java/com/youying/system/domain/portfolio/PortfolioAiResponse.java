package com.youying.system.domain.portfolio;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-08-30
 */
@Data
public class PortfolioAiResponse {
    private Long id;
    private Long portfolioId;
    private String name;
    private String theaterName;
    private String repertoireName;
    private Long ocrNo;
    private Long theaterId;
    private Long repertoireId;
    private Long repertoireTicketId;
    private Long digitalAvatarId;
    private Integer soldOut;
    private String theaterShortName;
    private String repertoireShortName;
    private Long scanningId;

    /**
     * 剧目剧场no
     */
    private String no;

    /**
     * 封面版式正面
     */
    private String coverFront;

    /**
     * 封面版式反面
     */
    private String coverReverse;

    /**
     * 普通电子票图片
     */
    private String commonImage;

    /**
     * 普通数字头像
     */
    private String digitalAvatarCommonImage;

    /**
     * 座位是否可重复
     */
    private Integer seatStatus;
}
