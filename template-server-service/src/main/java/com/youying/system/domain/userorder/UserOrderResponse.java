package com.youying.system.domain.userorder;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
public class UserOrderResponse {
    private Long id;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易编号
     */
    private String transactionId;

    /**
     * 支付编号
     */
    private String orderNo;

    /**
     * 预支付交易会话标识
     */
    private String prepayId;

    /**
     * 支付金额
     */
    private BigDecimal payPrice;

    /**
     * 退款金额
     */
    private BigDecimal refundPrice;

    /**
     * 支付状态（0待支付，1已支付）
     */
    private Integer payStatus;

    /**
     * 退款状态（0未退款，1已退款）
     */
    private Integer refundStatus;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 退款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;

    /**
     * 用户数字藏品领取记录ID
     */
    private Long userReceivingRecordsId;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 1：电子票、2：数字头像、3：纪念徽章、4：等级勋章 ID
     */
    private Long relationId;

    /**
     * 徽章类型（1：电子票、2：数字头像、3：纪念徽章、4：等级勋章）
     */
    private Integer badgeType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 等级勋章名称
     */
    private String rankMedalName;

    /**
     * 等级勋章名称等级
     */
    private String rankMedalLevel;

    /**
     * 等级勋章颜色
     */
    private String color;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 数字头像路径
     */
    private String digitalAvatarImage;

    /**
     * 封面版式正面
     */
    private String coverFront;

    /**
     * 封面版式反面
     */
    private String coverReverse;

    /**
     * 普通电子票图片
     */
    private String commonImage;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 时间
     */
    private String time;
}
