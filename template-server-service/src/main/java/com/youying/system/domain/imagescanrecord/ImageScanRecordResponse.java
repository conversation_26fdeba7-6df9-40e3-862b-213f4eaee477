package com.youying.system.domain.imagescanrecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-10-19
 */
@Data
public class ImageScanRecordResponse {
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 用户手机号码
     */
    private String phone;

    /**
     * 手机型号
     */
    private String deviceModel;

    /**
     * 扫码商品组合ID
     */
    private Long portfolioId;

    /**
     * 扫描图片路径
     */
    private String fileUrl;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 扫描结果
     */
    private String body;

    /**
     * 详情结果
     */
    private String wordList;

    /**
     * 领取情况 > 0 为已领取
     */
    private Integer getCount;

    /**
     * 扫描时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
