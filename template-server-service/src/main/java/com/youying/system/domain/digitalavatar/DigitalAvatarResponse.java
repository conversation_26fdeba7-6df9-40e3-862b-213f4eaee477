package com.youying.system.domain.digitalavatar;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.common.core.domain.entity.DigitalAvatarImage;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
public class DigitalAvatarResponse {
    private Long id;

    /**
     * 批次
     */
    private Integer batch;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 发行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 发行方
     */
    private String issuerName;

    /**
     * 发行数量
     */
    private Integer issuedQuantity;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    private Integer audit;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 驳回原因
     */
    private String reasonsRejection;

    /**
     * 领取数量
     */
    private Integer getCount;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 电子头像
     */
    private List<DigitalAvatarImage> digitalAvatarImageList;
}
