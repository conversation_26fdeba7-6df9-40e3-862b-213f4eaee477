package com.youying.system.domain.portfolio;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.common.core.domain.entity.DigitalAvatarImage;
import com.youying.common.core.domain.entity.RepertoireTicket;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-08-16
 */
@Data
public class PortfolioResponse {
    private Long id;

    /**
     * ocr编号
     */
    private String ocrNo;

    /**
     * 剧目剧场关联编号
     */
    private String no;

    /**
     * 组合名称
     */
    private String name;

    /**
     * 批次
     */
    private Integer batch;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 封面版式正面
     */
    private String coverFront;

    /**
     * 封面版式反面
     */
    private String coverReverse;

    /**
     * 电子票ID
     */
    private Long repertoireTicketId;

    /**
     * 数字头像ID
     */
    private Long digitalAvatarId;

    /**
     * 组合介绍
     */
    private String introduction;

    /**
     * 发放数量
     */
    private Integer issuedQuantity;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    private Integer audit;

    /**
     * 审核通过
     */
    private Integer auditFlag;

    /**
     * 审核通过时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditPassTime;

    /**
     * 驳回原因
     */
    private String reasonsRejection;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 领取人数
     */
    private Integer getNum;

    /**
     * 普通电子票图片
     */
    private String repertoireTicketCommonImage;

    /**
     * 普通数字头像图片
     */
    private String digitalAvatarCommonImage;

    /**
     * 售罄状态
     */
    private Integer soldOut;

    /**
     * 免责声明
     */
    private String statement;

    /**
     * 免责声明
     */
    private Long portfolioStatementId;

    /**
     * 升级数量
     */
    private Integer upgradeNum;

    /**
     * 免费发放
     */
    private Integer free;

    /**
     * 修改原因
     */
    private String updateCause;

    /**
     * 纸质票扫码ID
     */
    private Long scanningId;

    /**
     * 座位是否可重复
     */
    private Integer seatStatus;

    /**
     * 橱窗展示状态
     */
    private Integer lookStatus;

    /**
     * 数字头像组合
     */
    private List<DigitalAvatarImage> digitalAvatarImageList = new ArrayList<DigitalAvatarImage>();

    /**
     * 电子票
     */
    private RepertoireTicket repertoireTicket = new RepertoireTicket();
}
