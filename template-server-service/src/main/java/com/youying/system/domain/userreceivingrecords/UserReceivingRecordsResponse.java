package com.youying.system.domain.userreceivingrecords;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.common.core.domain.entity.RankMedalInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-01
 */
@Data
public class UserReceivingRecordsResponse {
    private Long id;

    /**
     * 用户手机号码
     */
    private String phone;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 等级勋章名称
     */
    private String rankMedalName;

    /**
     * 等级勋章名称等级
     */
    private String rankMedalLevel;

    /**
     * 等级勋章颜色
     */
    private String rankMedalColor;

    /**
     * 编号
     */
    private String no;

    /**
     * 区块链发放序列ID
     */
    private String sendId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 剧目标签
     */
    private String repertoireLabel;

    /**
     * 1：电子票、2：数字头像、3：纪念徽章、4：等级勋章 ID
     */
    private Long relationId;

    /**
     * 徽章类型（1：电子票、2：数字头像、3：纪念徽章、4：等级勋章）
     */
    private Integer badgeType;

    /**
     * 座位号
     */
    private String seatNumber;

    /**
     * 消费金额
     */
    private String amount;

    /**
     * 演出时间
     */
    private String time;

    /**
     * 城市
     */
    private String cityName;

    /**
     * 发行方
     */
    private String issuerName;

    /**
     * 消费金额
     */
    private BigDecimal expensePrice;

    /**
     * 图片
     */
    private String image;

    /**
     * 封面版式正面
     */
    private String coverFront;

    /**
     * 封面版式反面
     */
    private String coverReverse;

    /**
     * 消费次数
     */
    private Integer expenseNumber;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 等级勋章信息
     */
    private RankMedalInfo rankMedalInfo;

    /**
     * 纪念徽章封面图
     */
    private String coverPicture;

    /**
     * 等级徽章颜色
     */
    private String color;

    /**
     * 藏品等级徽章名称
     */
    private String collectionRankMedalName;

    /**
     * 藏品等级徽章等级
     */
    private String collectionRankMedalLevel;

    /**
     * 藏品等级徽章颜色
     */
    private String collectionColor;

    /**
     * 藏品编号
     */
    private String collectionNo;

    /**
     * 升级状态（0：待升级，1：已升级）
     */
    private String upgradeStatus;

    /**
     * 升级时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String upgradeTime;

    /**
     * 升级藏品路径
     */
    private String upgradeImage;

    /**
     * 纸质票扫码路径
     */
    private String fileUrl;

    /**
     * 演员信息
     */
    private List<String> actorInformationList;
}
