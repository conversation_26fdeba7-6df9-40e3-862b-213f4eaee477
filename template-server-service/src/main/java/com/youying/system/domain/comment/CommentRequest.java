package com.youying.system.domain.comment;

import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.page.PageDomain;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-06-06
 */
@Data
public class CommentRequest extends PageDomain {
    /**
     * 剧场ID
     */
    private String theaterId;

    /**
     * 剧目ID
     */
    private String repertoireId;

    /**
     * 回复状态
     */
    private String status;

    /**
     * 置顶
     */
    private String top;

    /**
     * 删除状态
     */
    private String deleted;

    /**
     * 评论ID （父级）
     */
    private Long parentId;

    /**
     * 创建时间
     */
    private TimeRequest time = new TimeRequest();
}
