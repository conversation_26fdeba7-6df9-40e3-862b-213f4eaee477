package com.youying.system.domain.notify;

import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.page.PageDomain;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-07-10
 */
@Data
public class NotifyRequest extends PageDomain {
    /**
     * 端口（1商家、2用户）
     */
    private String port;

    /**
     * 状态
     */
    private String status;

    /**
     * 推送类型（1立即推送、2手动发布、3定时推送）
     */
    private String pushType;

    /**
     * 创建时间
     */
    private TimeRequest time = new TimeRequest();
}
