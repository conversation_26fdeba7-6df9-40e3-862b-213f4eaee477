package com.youying.system.domain.issue;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
public class IssueResponse {
    private Long id;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 剧场封面图
     */
    private String theaterCoverPicture;

    /**
     * 剧目封面图
     */
    private String repertoireCoverPicture;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 回复人
     */
    private String replyName;

    /**
     * 回复人头像
     */
    private String replyAvatar;

    /**
     * 商家回复名称
     */
    private String merchantReplyName;

    /**
     * 商家回复头像
     */
    private String merchantReplyAvatar;

    /**
     * 等级勋章名称
     */
    private String rankMedalName;

    /**
     * 等级勋章名称等级
     */
    private String rankMedalLevel;

    /**
     * 等级勋章颜色
     */
    private String rankMedalColor;

    /**
     * 状态
     */
    private String status;

    /**
     * 内容
     */
    private String content;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 回复条数
     */
    private Integer replyCount;

    /**
     * 用户回答列表
     */
    private List<IssueResponse> userList = new ArrayList<IssueResponse>();
}
