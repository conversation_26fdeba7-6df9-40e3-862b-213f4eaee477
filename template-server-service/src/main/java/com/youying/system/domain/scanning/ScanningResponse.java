package com.youying.system.domain.scanning;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.common.core.domain.entity.ScanningInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-10-24
 */
@Data
public class ScanningResponse {
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 是否默认
     */
    private Integer defaultFlag;

    /**
     * 启用状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 规则详情
     */
    private List<ScanningInfo> scanningInfoList = new ArrayList<ScanningInfo>();
}
