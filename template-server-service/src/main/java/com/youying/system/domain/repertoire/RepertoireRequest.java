package com.youying.system.domain.repertoire;

import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.page.PageDomain;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
public class RepertoireRequest extends PageDomain {
    /**
     * 剧场ID
     */
    private String theaterId;

    /**
     * 商家ID
     */
    private String merchantId;

    /**
     * 剧目标签
     */
    private String repertoireLabel;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    private String audit;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private TimeRequest time = new TimeRequest();

    /**
     * 演出时间
     */
    private TimeRequest showTime = new TimeRequest();
}
