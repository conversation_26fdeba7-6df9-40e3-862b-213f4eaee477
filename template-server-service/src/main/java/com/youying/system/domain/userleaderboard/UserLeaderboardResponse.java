package com.youying.system.domain.userleaderboard;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-01-09
 */
@Data
public class UserLeaderboardResponse {
    /**
     * 榜单名称
     */
    private String name;

    /**
     * 剧目名称
     */
    private String repertoireName;

    /**
     * 榜单名称
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime;
}
