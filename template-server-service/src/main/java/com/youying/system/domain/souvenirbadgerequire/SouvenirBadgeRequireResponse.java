package com.youying.system.domain.souvenirbadgerequire;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-06-30
 */
@Data
public class SouvenirBadgeRequireResponse {
    private Long id;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 纪念徽章ID
     */
    private Long souvenirBadgeId;

    /**
     * 等级勋章（大于设置等级）
     */
    private Long rankMedalId;

    /**
     * 扫描电子票中含有设置的剧场信息次数（剧目ID）电子票扫描场次
     */
    private Integer repertoireInfoDetailId;

    /**
     * 观看剧场演出（剧目剧场ID）观看某场次
     */
    private Long repertoireInfoId;

    /**
     * 观看时间段
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 观看时间段
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 扫描次数
     */
    private Integer lookNumber;

    /**
     * 时间段内观看次数
     */
    private Integer timeLookNumber;

    /**
     * 等级勋章名称
     */
    private String rankMedalName;

    /**
     * 等级勋章等级
     */
    private String rankMedalLevel;

    /**
     * 场次开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date repertoireStartTime;

    /**
     * 场次结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date repertoireEndTime;

    /**
     * 指定名单
     */
    private String whiteNameList;
}
