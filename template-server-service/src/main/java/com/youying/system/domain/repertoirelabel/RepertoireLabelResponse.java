package com.youying.system.domain.repertoirelabel;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-05-28
 */
@Data
public class RepertoireLabelResponse {
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
