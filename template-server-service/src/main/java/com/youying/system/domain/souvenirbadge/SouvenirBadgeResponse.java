package com.youying.system.domain.souvenirbadge;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.system.domain.souvenirbadgerequire.SouvenirBadgeRequireResponse;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
public class SouvenirBadgeResponse {
    private Long id;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 商家
     */
    private String merchantName;

    /**
     * 藏品名称
     */
    private String name;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 徽章模型，支持上传3D模型，大小100M以内
     */
    private String model;

    /**
     * 批次
     */
    private Integer batch;

    /**
     * 售罄状态
     */
    private Integer soldOut;

    /**
     * 发行方
     */
    private String issuerName;

    /**
     * 发放数量
     */
    private Integer issuedQuantity;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 藏品简介
     */
    private String introduction;

    /**
     * 封面图URL
     */
    private String coverPicture;

    /**
     * 短信通知
     */
    private Integer smsNotify;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    private Integer audit;

    /**
     * 领取数量
     */
    private Integer receivedNumber;

    /**
     * 驳回原因
     */
    private String reasonsRejection;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 橱窗展示状态
     */
    private Integer lookStatus;

    /**
     * 规则
     */
    private SouvenirBadgeRequireResponse souvenirBadgeRequire;
}
