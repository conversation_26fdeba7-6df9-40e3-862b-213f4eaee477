package com.youying.system.domain.repertoireticket;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
public class RepertoireTicketResponse {
    private Long id;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 商家
     */
    private String merchantName;

    /**
     * 批次
     */
    private Integer sort;

    /**
     * 剧场ID
     */
    private Long theaterId;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 封面版式正面
     */
    private String coverFront;

    /**
     * 封面版式反面
     */
    private String coverReverse;

    /**
     * 发行方
     */
    private String issuerName;

    /**
     * 发放数量
     */
    private Integer issuedQuantity;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    private Integer audit;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 驳回原因
     */
    private String reasonsRejection;

    /**
     * 领取数量
     */
    private Integer getCount;
}
