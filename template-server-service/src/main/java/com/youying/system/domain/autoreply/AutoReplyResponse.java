package com.youying.system.domain.autoreply;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
public class AutoReplyResponse {
    private Long id;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 群发内容
     */
    private String body;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
