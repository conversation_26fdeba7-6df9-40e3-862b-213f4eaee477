package com.youying.system.domain.entity;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 电子票根/电子票面
 */
@Data
public class Eticket {
    private Integer id;
    private String name;
    private String coverImage;
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private List<Integer> ids; // 批量删除用

}