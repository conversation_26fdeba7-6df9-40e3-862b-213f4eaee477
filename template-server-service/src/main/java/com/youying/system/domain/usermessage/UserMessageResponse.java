package com.youying.system.domain.usermessage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
public class UserMessageResponse {
    private Long id;

    /**
     * 剧场
     */
    private String theaterName;

    /**
     * 剧场ID
     */
    private String theaterId;

    /**
     * 剧目
     */
    private String repertoireName;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧场封面图
     */
    private String theaterCoverPicture;

    /**
     * 剧目封面图
     */
    private String repertoireCoverPicture;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户手机号码
     */
    private String phone;

    /**
     * 等级勋章名称
     */
    private String rankMedalName;

    /**
     * 勋章等级
     */
    private String rankMedalLevel;

    /**
     * 等级勋章颜色
     */
    private String rankMedalColor;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 内容
     */
    private String body;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 关注时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date treasureTime;

    /**
     * 最近回复时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date userLastTime;

    /**
     * 剧目剧场回复
     */
    private Long userMerchantId;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 回复记录
     */
    private List<UserMessageResponse> userMessageInfoList = new ArrayList<UserMessageResponse>();
}
