package com.youying.system.domain.repertoireinfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.youying.common.core.domain.entity.RepertoireInfoDetail;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
public class RepertoireInfoResponse {
    private Long id;

    /**
     * 商家ID
     */
    private Long merchantId;

    /**
     * 剧目商家
     */
    private String merchantName;

    /**
     * 省
     */
    private Long provId;

    /**
     * 剧场ID (0为暂无)
     */
    private Long theaterId;

    /**
     * 剧场ID (0为暂无)
     */
    private String theaterName;

    /**
     * 剧目ID
     */
    private Long repertoireId;

    /**
     * 剧目ID
     */
    private String repertoireName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    private Integer audit;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 详情
     */
    private List<RepertoireInfoDetail> repertoireInfoDetailList;
}
