package com.youying.system.domain.userticketgroup;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-01-09
 */
@Data
public class UserTicketGroupResponse {
    private Long id;
    private Long userId;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 剧目名称
     */
    private List<String> repertoireNameList = new ArrayList<String>();

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
