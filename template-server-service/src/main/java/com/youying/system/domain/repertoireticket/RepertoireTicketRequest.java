package com.youying.system.domain.repertoireticket;

import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.page.PageDomain;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
public class RepertoireTicketRequest extends PageDomain {
    /**
     * 商家ID
     */
    private String merchantId;

    /**
     * 剧目ID
     */
    private String repertoireId;

    /**
     * 剧场ID
     */
    private String theaterId;

    /**
     * 审核状态
     */
    private String audit;

    /**
     * 创建时间
     */
    private TimeRequest time = new TimeRequest();
}
