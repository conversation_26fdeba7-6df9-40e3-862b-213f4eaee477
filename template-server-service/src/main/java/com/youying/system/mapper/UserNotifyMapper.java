package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserNotify;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户推送信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface UserNotifyMapper extends BaseMapper<UserNotify> {

    /**
     * 批量添加推送信息
     *
     * @param userNotifyList
     */
    void insertBatchNotifyInfo(@Param("userNotifyList") List<UserNotify> userNotifyList);

    /**
     * 查询推送消息详情
     *
     * @param id
     * @return
     */
    UserNotify details(@Param("id") Long id);
}
