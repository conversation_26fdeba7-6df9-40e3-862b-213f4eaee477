package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.Theater;
import com.youying.system.domain.common.PullRequest;
import com.youying.system.domain.theater.TheaterRequest;
import com.youying.system.domain.theater.TheaterResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 剧场表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface TheaterMapper extends BaseMapper<Theater> {

    /**
     * 查询剧场表列表(分页)
     *
     * @param request
     * @return
     */
    List<TheaterResponse> listByPage(TheaterRequest request);

    /**
     * 查询剧场详情
     *
     * @param id
     * @return
     */
    TheaterResponse findTheaterInfo(@Param("id") Long id);

    /**
     * 下拉
     *
     * @param request
     * @return
     */
    List<PullResponse> pull(PullRequest request);

    /**
     * 首页-查询剧场添加数量
     *
     * @param time
     * @return
     */
    Long findTheaterAddCount(@Param("time") TimeRequest time);
}
