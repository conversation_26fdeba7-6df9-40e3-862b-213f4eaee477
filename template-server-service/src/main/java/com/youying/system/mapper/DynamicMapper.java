package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Dynamic;
import com.youying.system.domain.dynamic.DynamicRequest;
import com.youying.system.domain.dynamic.DynamicResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 剧目剧场动态表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface DynamicMapper extends BaseMapper<Dynamic> {
    /**
     * 查询剧目剧场动态表列表(分页)
     *
     * @param request
     * @return
     */
    List<DynamicResponse> listByPage(DynamicRequest request);

    /**
     * 查询剧目剧场动态表详情
     *
     * @param id
     * @return
     */
    DynamicResponse details(@Param("id") Long id);
}
