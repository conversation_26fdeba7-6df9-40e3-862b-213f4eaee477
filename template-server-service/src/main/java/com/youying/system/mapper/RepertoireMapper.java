package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.system.domain.common.PullRequest;
import com.youying.system.domain.repertoire.RepertoireRequest;
import com.youying.system.domain.repertoire.RepertoireResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 剧目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RepertoireMapper extends BaseMapper<Repertoire> {

    /**
     * 查询剧目表列表(分页)
     *
     * @param request
     * @return
     */
    List<RepertoireResponse> listByPage(RepertoireRequest request);

    /**
     * 查询剧目表详情(部分)
     *
     * @param id
     * @return
     */
    RepertoireResponse findRepertoireInfo(@Param("id") Long id);

    /**
     * 下拉
     *
     * @param request
     * @return
     */
    List<PullResponse> pull(PullRequest request);

    /**
     * 首页-查询剧目段添加数量
     *
     * @param time
     * @return
     */
    Long findRepertoireAddCount(@Param("time") TimeRequest time);
}
