package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserTicketGroup;
import com.youying.system.domain.user.UserRequest;
import com.youying.system.domain.userticketgroup.UserTicketGroupResponse;

import java.util.List;

/**
 * <p>
 * 用户电子票分组表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
public interface UserTicketGroupMapper extends BaseMapper<UserTicketGroup> {

    /**
     * 查询用户电子票分组
     *
     * @param request
     * @return
     */
    List<UserTicketGroupResponse> findUserTicketGroup(UserRequest request);
}
