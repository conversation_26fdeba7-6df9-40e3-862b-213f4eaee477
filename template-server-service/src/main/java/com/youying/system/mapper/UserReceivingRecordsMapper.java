package com.youying.system.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.system.domain.userreceivingrecords.UserGetResponse;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsEx;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsRequest;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsResponse;

/**
 * <p>
 * 用户数字藏品领取记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface UserReceivingRecordsMapper extends BaseMapper<UserReceivingRecords> {
    /**
     * 查询用户领取数字藏品
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> listByPage(UserReceivingRecordsRequest request);

    /**
     * 查询藏品领取详情
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> findCollectionRecipient(UserReceivingRecordsRequest request);

    /**
     * 首页-查询用户领取数字藏品数
     *
     * @param time
     * @param badgeType
     * @return
     */
    Long findUserReceivingRecordsAddCount(@Param("time") TimeRequest time,
            @Param("badgeType") Integer badgeType);

    /**
     * 导出消费记录
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsEx> export(UserReceivingRecordsRequest request);

    /**
     * 查询用户观影剧目、剧场次数
     *
     * @param relationId
     * @param code
     * @return
     */
    List<UserGetResponse> findUserLookCount(@Param("relationId") Long relationId,
            @Param("code") Integer code);

    /**
     * @param userId
     * @param theaterId
     * @param lookNumber
     * @param rankMedalId
     * @param rankMedalInfoId
     * @param repertoireInfoId
     * @param startTime
     * @param endTime
     * @return
     */
    Long findUserLookAssignRepertoire(@Param("userId") Long userId,
            @Param("theaterId") Long theaterId,
            @Param("lookNumber") Integer lookNumber,
            @Param("rankMedalId") Long rankMedalId,
            @Param("rankMedalInfoId") Long rankMedalInfoId,
            @Param("repertoireInfoId") Long repertoireInfoId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);

    /**
     * 查询重新生成电子票信息
     * 
     * @param id
     * @return
     */
    @Select("SELECT t.no,t.seat_number as seatNumber,t.price,t.time as playTime,t.image as currentEticketImage,t.upgrade_image as currentUpgradeEticketImage, t1.name as repertoireName,t1.cover_picture as repertoireCoverPicture,t2.common_image as eticketBackImage,t3.avatar,t3.name FROM user_receiving_records t "
            + "left join t_repertoire t1 on t.repertoire_id = t1.id "
            + "left join t_portfolio_info t2 on t2.id = t.portfolio_info_id "
            + "left join t_user t3 on t3.id = t.user_id "
            + "WHERE t.id = #{id}")
    Map<String, Object> findRegerateUserETicketImageInfo(Long id);
}
