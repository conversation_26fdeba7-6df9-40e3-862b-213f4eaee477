package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Message;
import com.youying.system.domain.message.MessageRequest;
import com.youying.system.domain.message.MessageResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 群发消息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface MessageMapper extends BaseMapper<Message> {

    /**
     * 查询群发消息表列表(分页)
     *
     * @param request
     * @return
     */
    List<MessageResponse> listByPage(MessageRequest request);

    /**
     * 查询群发消息表详情
     *
     * @param id
     * @return
     */
    MessageResponse details(@Param("id") Long id);
}
