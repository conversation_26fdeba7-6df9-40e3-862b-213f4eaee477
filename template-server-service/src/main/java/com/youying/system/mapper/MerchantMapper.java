package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.Merchant;
import com.youying.system.domain.common.PullRequest;
import com.youying.system.domain.merchant.MerchantRequest;
import com.youying.system.domain.merchant.MerchantResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商家表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface MerchantMapper extends BaseMapper<Merchant> {

    /**
     * 查询商家表列表(分页)
     *
     * @param request
     * @return
     */
    List<MerchantResponse> listByPage(MerchantRequest request);

    /**
     * 下拉
     *
     * @param request
     * @return
     */
    List<PullResponse> pull(PullRequest request);

    /**
     * 首页-查询商家增量
     *
     * @param time
     * @return
     */
    Long findMerchantAddCount(@Param("time") TimeRequest time);
}
