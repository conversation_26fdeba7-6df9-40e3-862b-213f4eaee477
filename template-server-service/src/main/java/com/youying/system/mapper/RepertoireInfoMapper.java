package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.RepertoireInfo;
import com.youying.system.domain.repertoireinfo.RepertoireInfoRequest;
import com.youying.system.domain.repertoireinfo.RepertoireInfoResponse;

import java.util.List;

/**
 * <p>
 * 剧目场次信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RepertoireInfoMapper extends BaseMapper<RepertoireInfo> {

    /**
     * 查询剧目场次信息表列表(分页)
     *
     * @param request
     * @return
     */
    List<RepertoireInfoResponse> listByPage(RepertoireInfoRequest request);
}
