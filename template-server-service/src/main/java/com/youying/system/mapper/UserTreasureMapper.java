package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.UserTreasure;
import com.youying.system.domain.common.CountResponse;
import com.youying.system.domain.usertreasure.UserTreasureEx;
import com.youying.system.domain.usertreasure.UserTreasureRequest;
import com.youying.system.domain.usertreasure.UserTreasureResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户剧目剧场收藏表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface UserTreasureMapper extends BaseMapper<UserTreasure> {

    /**
     * 首页-查询剧目剧场关注人数
     *
     * @param time
     * @return
     */
    List<UserTreasureResponse> findUserTreasureAddCount(@Param("time") TimeRequest time);

    /**
     * 首页-查询剧目关注人数
     *
     * @param time
     * @return
     */
    List<CountResponse> findUserTreasureRepertoireCount(@Param("time") TimeRequest time);

    /**
     * 首页-查询剧目关注人数
     *
     * @param time
     * @return
     */
    List<CountResponse> findUserTreasureTheaterCount(@Param("time") TimeRequest time);

    /**
     * 查询关注用户统计(剧目)
     *
     * @param request
     * @return
     */
    List<UserTreasureResponse> findRepertoireFans(UserTreasureRequest request);

    /**
     * 查询关注用户统计(剧场)
     *
     * @param request
     * @return
     */
    List<UserTreasureResponse> findTheaterFans(UserTreasureRequest request);

    /**
     * 导出关注用户统计(剧目)
     *
     * @param request
     * @return
     */
    List<UserTreasureEx> exportRepertoireFans(UserTreasureRequest request);

    /**
     * 导出关注用户统计(剧场)
     *
     * @param request
     * @return
     */
    List<UserTreasureEx> exportTheaterFans(UserTreasureRequest request);
}
