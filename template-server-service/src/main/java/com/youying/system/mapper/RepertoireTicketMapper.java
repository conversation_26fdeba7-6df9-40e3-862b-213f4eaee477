package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.system.domain.repertoireticket.RepertoireTicketRequest;
import com.youying.system.domain.repertoireticket.RepertoireTicketResponse;

import java.util.List;

/**
 * <p>
 * 剧目电子票表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RepertoireTicketMapper extends BaseMapper<RepertoireTicket> {

    /**
     * 查询剧目电子票表列表(分页)
     *
     * @param request
     * @return
     */
    List<RepertoireTicketResponse> listByPage(RepertoireTicketRequest request);
}
