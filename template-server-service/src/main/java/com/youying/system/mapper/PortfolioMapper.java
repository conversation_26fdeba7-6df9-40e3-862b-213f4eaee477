package com.youying.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.system.domain.portfolio.PortfolioRequest;
import com.youying.system.domain.portfolio.PortfolioResponse;

/**
 * <p>
 * 藏品组合表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface PortfolioMapper extends BaseMapper<Portfolio> {

    /**
     * 藏品组合表列表
     *
     * @param request
     * @return
     */
    List<PortfolioResponse> listByPage(PortfolioRequest request);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    PortfolioResponse details(@Param("id") Long id);

    /**
     * 插入藏品组合记录
     *
     * @param portfolio 藏品组合信息
     * @return 影响行数
     */
    int insertPortfolio(Portfolio portfolio);

    // PortfolioAiResponse find(@Param("repertoireName") String repertoireName,
    // @Param("theaterName") String theaterName);
}
