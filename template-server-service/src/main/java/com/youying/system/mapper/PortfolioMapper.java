package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.system.domain.portfolio.PortfolioRequest;
import com.youying.system.domain.portfolio.PortfolioResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 藏品组合表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface PortfolioMapper extends BaseMapper<Portfolio> {

    /**
     * 藏品组合表列表
     *
     * @param request
     * @return
     */
    List<PortfolioResponse> listByPage(PortfolioRequest request);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    PortfolioResponse details(@Param("id") Long id);

    // PortfolioAiResponse find(@Param("repertoireName") String repertoireName, @Param("theaterName") String theaterName);
}
