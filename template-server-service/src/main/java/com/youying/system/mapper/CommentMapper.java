package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Comment;
import com.youying.system.domain.comment.CommentRequest;
import com.youying.system.domain.comment.CommentResponse;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 剧目剧场评论 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface CommentMapper extends BaseMapper<Comment> {
    /**
     * 查询剧目剧场评论列表(分页)
     *
     * @param request
     * @return
     */
    List<CommentResponse> listByPage(CommentRequest request);

    /**
     * 查询评论详情
     *
     * @param id
     * @return
     */
    @Select("select * from t_comment where id = #{id}")
    Comment findCommentById(@Param("id") Long id);

    /**
     * 查询剧目剧场评论详情
     *
     * @param id
     * @return
     */
    CommentResponse details(@Param("id") Long id);
}
