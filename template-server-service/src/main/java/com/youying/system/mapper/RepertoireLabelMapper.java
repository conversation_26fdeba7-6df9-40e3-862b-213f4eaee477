package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.RepertoireLabel;
import com.youying.system.domain.repertoirelabel.RepertoireLabelRequest;
import com.youying.system.domain.repertoirelabel.RepertoireLabelResponse;

import java.util.List;

/**
 * <p>
 * 剧目标签表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RepertoireLabelMapper extends BaseMapper<RepertoireLabel> {

    /**
     * 查询剧目标签表列表(分页)
     *
     * @param request
     * @return
     */
    List<RepertoireLabelResponse> listByPage(RepertoireLabelRequest request);
}
