package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Notify;
import com.youying.system.domain.notify.NotifyRequest;

import java.util.List;

/**
 * <p>
 * 通知表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface NotifyMapper extends BaseMapper<Notify> {

    /**
     * 查询推送通知
     *
     * @return
     */
    List<Notify> findPushNotify();

    /**
     * 修改通知推送状态
     *
     * @param notify
     */
    void updateNotifyById(Notify notify);

    /**
     * 通知列表
     *
     * @param request
     * @return
     */
    List<Notify> listByPage(NotifyRequest request);
}
