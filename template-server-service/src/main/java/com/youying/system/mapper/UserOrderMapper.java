package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.UserOrder;
import com.youying.system.domain.userorder.UserOrderEx;
import com.youying.system.domain.userorder.UserOrderRequest;
import com.youying.system.domain.userorder.UserOrderResponse;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 用户订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface UserOrderMapper extends BaseMapper<UserOrder> {
    /**
     * 用户订单列表
     *
     * @param request
     * @return
     */
    List<UserOrderResponse> listByPage(UserOrderRequest request);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    UserOrderResponse details(@Param("id") Long id);

    /**
     * 查询商家订单总价
     *
     * @param time
     * @return
     */
    BigDecimal findOrderSumPrice(@Param("time") TimeRequest time);

    /**
     * 导出订单信息
     *
     * @param request
     * @return
     */
    List<UserOrderEx> export(UserOrderRequest request);
}
