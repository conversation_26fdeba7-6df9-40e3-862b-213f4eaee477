package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.User;
import com.youying.system.domain.common.CountResponse;
import com.youying.system.domain.user.UserRequest;
import com.youying.system.domain.user.UserResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface UserMapper extends BaseMapper<User> {

    /**
     * 删除用户关联信息
     *
     * @param ids
     */
    void deleteUser(@Param("ids") List<Long> ids);

    /**
     * 查询用户表列表(分页)
     *
     * @param request
     * @return
     */
    List<UserResponse> listByPage(UserRequest request);

    /**
     * 首页-查询用户性别数量
     *
     * @param time
     * @return
     */
    List<CountResponse> findUserSexCount(@Param("time") TimeRequest time);
}
