package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.AdvertisingPicture;
import com.youying.system.domain.advertisingpicture.AdvertisingPictureRequest;

import java.util.List;

/**
 * <p>
 * 广告轮播图表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface AdvertisingPictureMapper extends BaseMapper<AdvertisingPicture> {

    /**
     * 查询广告轮播图表列表(分页)
     *
     * @param request
     * @return
     */
    List<AdvertisingPicture> listByPage(AdvertisingPictureRequest request);
}
