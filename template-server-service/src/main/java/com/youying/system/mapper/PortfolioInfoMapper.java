package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.PortfolioInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 * 藏品组合表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-24
 */
public interface PortfolioInfoMapper extends BaseMapper<PortfolioInfo> {

    /**
     * 关闭商品老数据
     *
     * @param portfolioId
     */
    @Update("UPDATE t_portfolio_info SET `status` = '0' WHERE portfolio_id = #{portfolioId}")
    void updateStatus(@Param("portfolioId") Long portfolioId);
}
