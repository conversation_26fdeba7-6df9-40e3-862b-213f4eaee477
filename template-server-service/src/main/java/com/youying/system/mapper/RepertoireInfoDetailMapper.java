package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.RepertoireInfoDetail;
import com.youying.system.domain.repertoireinfodetail.RepertoireInfoDetailResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 剧目剧场场次表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RepertoireInfoDetailMapper extends BaseMapper<RepertoireInfoDetail> {
    /**
     * 根据剧目ID查询场次表列表
     *
     * @param repertoireId
     * @return
     */
    List<RepertoireInfoDetailResponse> listByRepertoireId(@Param("repertoireId") Long repertoireId);
}
