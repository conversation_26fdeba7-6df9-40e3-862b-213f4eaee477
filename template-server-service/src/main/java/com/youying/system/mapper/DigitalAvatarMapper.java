package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.DigitalAvatar;
import com.youying.system.domain.digitalavatar.DigitalAvatarRequest;
import com.youying.system.domain.digitalavatar.DigitalAvatarResponse;

import java.util.List;

/**
 * <p>
 * 数字头像表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface DigitalAvatarMapper extends BaseMapper<DigitalAvatar> {

    /**
     * 查询数字头像表列表(分页)
     *
     * @param request
     * @return
     */
    List<DigitalAvatarResponse> listByPage(DigitalAvatarRequest request);
}
