package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.UserLeaderboard;
import com.youying.system.domain.user.UserRequest;
import com.youying.system.domain.userleaderboard.UserLeaderboardResponse;

import java.util.List;

/**
 * <p>
 * 用户排行榜表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
public interface UserLeaderboardMapper extends BaseMapper<UserLeaderboard> {

    /**
     * 查询用户排行榜表
     *
     * @param request
     * @return
     */
    List<UserLeaderboardResponse> findUserLeaderboard(UserRequest request);
}
