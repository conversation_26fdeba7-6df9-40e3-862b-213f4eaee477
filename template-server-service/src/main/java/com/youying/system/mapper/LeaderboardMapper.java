package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Leaderboard;
import com.youying.common.core.page.PageDomain;
import com.youying.system.domain.leaderboard.LeaderboardRequest;
import com.youying.system.domain.userleaderboard.LeaderboardResponse;

import java.util.List;

/**
 * <p>
 * 榜单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
public interface LeaderboardMapper extends BaseMapper<Leaderboard> {

    /**
     * 查询系统榜单
     *
     * @param pageDomain
     * @return
     */
    List<Leaderboard> listByPage(PageDomain pageDomain);

    /**
     * 榜单剧目统计
     *
     * @param request
     * @return
     */
    List<LeaderboardResponse> findRepertoireLeaderboard(LeaderboardRequest request);
}
