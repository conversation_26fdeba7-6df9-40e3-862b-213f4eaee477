package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.Area;
import com.youying.system.domain.common.area.Tree;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 地区表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface AreaMapper extends BaseMapper<Area> {
    /**
     * 查询全部地区
     *
     * @return
     */
    @Select("SELECT id , fullname AS `name` , parent_id FROM t_area WHERE level_type > 0 ORDER BY id")
    List<Tree> findAreaAll();

    /**
     * 地区详情
     *
     * @param id
     * @return
     */
    @Select("SELECT id , fullname AS `name` , sort , remark FROM t_area WHERE id = #{id}")
    Area details(@Param("id") Long id);
}
