package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.SouvenirBadge;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeRequest;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeResponse;

import java.util.List;

/**
 * <p>
 * 剧场纪念徽章表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface SouvenirBadgeMapper extends BaseMapper<SouvenirBadge> {

    /**
     * 查询剧场纪念徽章表列表(分页)
     *
     * @param request
     * @return
     */
    List<SouvenirBadgeResponse> listByPage(SouvenirBadgeRequest request);
}
