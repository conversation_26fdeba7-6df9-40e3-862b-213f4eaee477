package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.domain.entity.Scanning;
import com.youying.system.domain.scanning.ScanningRequest;
import com.youying.system.domain.scanning.ScanningResponse;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
public interface ScanningMapper extends BaseMapper<Scanning> {

    /**
     * 查询默认规则
     *
     * @return
     */
    @Select("SELECT * FROM t_scanning WHERE default_flag = '1'")
    Scanning findScanningDefault();

    /**
     * 查询纸质票扫描规则
     *
     * @param request
     * @return
     */
    List<ScanningResponse> listByPage(ScanningRequest request);

    /**
     * 纸质票扫描规则详情
     *
     * @param id
     * @return
     */
    ScanningResponse details(@Param("id") Long id);

    /**
     * 下拉
     *
     * @return
     */
    @Select("SELECT id , `name` FROM t_scanning WHERE `status` = '1' ORDER BY default_flag DESC , create_time DESC , id")
    List<PullResponse> pull();
}
