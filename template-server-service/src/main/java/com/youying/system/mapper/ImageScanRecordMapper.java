package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.ImageScanRecord;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.common.core.page.PageDomain;
import com.youying.system.domain.imagescanrecord.ImageScanRecordResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public interface ImageScanRecordMapper extends BaseMapper<ImageScanRecord> {

    /**
     * 查询纸质票扫描记录
     *
     * @param pageDomain
     * @return
     */
    List<ImageScanRecordResponse> list(PageDomain pageDomain);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ImageScanRecordResponse details(@Param("id") Long id);

    List<UserReceivingRecords> find(@Param("ids") List<Long> ids);
}
