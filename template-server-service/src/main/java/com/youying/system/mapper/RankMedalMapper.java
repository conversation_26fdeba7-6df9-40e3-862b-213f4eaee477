package com.youying.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.common.core.domain.entity.RankMedal;
import com.youying.system.domain.rankmedal.RankMedalRequest;
import com.youying.system.domain.rankmedal.RankMedalResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 等级勋章表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RankMedalMapper extends BaseMapper<RankMedal> {

    /**
     * 查询等级勋章表列表(分页)
     *
     * @param request
     * @return
     */
    List<RankMedalResponse> listByPage(RankMedalRequest request);

    /**
     * 查询等级勋章表详情
     *
     * @param id
     * @return
     */
    RankMedalResponse details(@Param("id") Long id);
}
