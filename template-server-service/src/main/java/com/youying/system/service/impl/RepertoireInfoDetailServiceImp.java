package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.RepertoireInfoDetail;
import com.youying.system.domain.repertoireinfodetail.RepertoireInfoDetailResponse;
import com.youying.system.mapper.RepertoireInfoDetailMapper;
import com.youying.system.service.RepertoireInfoDetailService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 剧目剧场场次表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class RepertoireInfoDetailServiceImp extends ServiceImpl<RepertoireInfoDetailMapper, RepertoireInfoDetail> implements RepertoireInfoDetailService {

    /**
     * 根据剧目ID查询场次表列表
     *
     * @param repertoireId
     * @return
     */
    @Override
    public List<RepertoireInfoDetailResponse> listByRepertoireId(Long repertoireId) {
        return baseMapper.listByRepertoireId(repertoireId);
    }
}
