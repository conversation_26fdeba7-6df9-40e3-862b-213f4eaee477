package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.RankMedalInfo;
import com.youying.common.core.domain.entity.SouvenirBadge;
import com.youying.common.core.domain.entity.SouvenirBadgeRequire;
import com.youying.common.core.domain.entity.UserPushCollection;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.enums.Enums.BadgeTypeFlag;
import com.youying.common.enums.Enums.MerchantCategoryFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.StringUtils;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeRequest;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeResponse;
import com.youying.system.domain.userreceivingrecords.UserGetResponse;
import com.youying.system.mapper.SouvenirBadgeMapper;
import com.youying.system.service.RankMedalInfoService;
import com.youying.system.service.SouvenirBadgeRequireService;
import com.youying.system.service.SouvenirBadgeService;
import com.youying.system.service.UserPushCollectionService;
import com.youying.system.service.UserReceivingRecordsService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 剧场纪念徽章表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class SouvenirBadgeServiceImp extends ServiceImpl<SouvenirBadgeMapper, SouvenirBadge> implements SouvenirBadgeService {
    @Autowired
    private SouvenirBadgeRequireService souvenirBadgeRequireService;
    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;
    @Autowired
    private UserPushCollectionService userPushCollectionService;
    @Autowired
    private RankMedalInfoService rankMedalInfoService;

    /**
     * 查询剧场纪念徽章表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<SouvenirBadgeResponse> listByPage(SouvenirBadgeRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 删除剧场纪念徽章
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public Integer delete(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            for (Long id : ids) {
                SouvenirBadge souvenirBadge = getById(id);
                if (souvenirBadge != null && AuditFlag.PASS.getCode().equals(souvenirBadge.getAudit())) {
                    throw new ServiceException("纪念徽章已审核通过，无法删除");
                }
                // 删除纪念徽章
                souvenirBadgeRequireService.deleteBySouvenirBadgeId(id);
            }
            removeBatchByIds(ids);
        }
        return ids.size();
    }

    /**
     * 根据商家ID判断是否添加纪念徽章
     *
     * @param merchantId
     * @return
     */
    @Override
    public Long findSouvenirBadgeCountByMerchantId(Long merchantId) {
        return count(new LambdaQueryWrapper<SouvenirBadge>()
                .eq(SouvenirBadge::getMerchantId, merchantId));
    }

    /**
     * 根据剧场ID查询纪念徽章
     *
     * @param theaterId
     * @return
     */
    @Override
    public Long findSouvenirBadgeCountByTheaterId(List<Long> theaterId) {
        return baseMapper.selectCount(new LambdaQueryWrapper<SouvenirBadge>()
                .in(SouvenirBadge::getTheaterId, theaterId)
                .eq(SouvenirBadge::getAudit, AuditFlag.PASS.getCode()));
    }

    /**
     * 根据剧场ID批量删除剧场纪念徽章
     *
     * @param ids
     */
    @Override
    @Transactional
    public void batchDeleteByTheaterId(List<Long> ids) {
        baseMapper.delete(new LambdaQueryWrapper<SouvenirBadge>()
                .in(SouvenirBadge::getTheaterId, ids));
    }

    /**
     * 发送纪念徽章
     *
     * @param souvenirBadge
     */
    @Override
    @Transactional
    public void sendSouvenirBadge(SouvenirBadge souvenirBadge) {
        Long souvenirBadgeId = souvenirBadge.getId();
        Long theaterId = souvenirBadge.getTheaterId();
        Integer issuedQuantity = souvenirBadge.getIssuedQuantity();
        List<UserPushCollection> userPushCollectionList = new ArrayList<UserPushCollection>();
        UserPushCollection userPushCollection = new UserPushCollection();
        List<UserGetResponse> userGetResponseList = userReceivingRecordsService.findUserLookCount(theaterId, MerchantCategoryFlag.THEATER.getCode());
        if (CollectionUtils.isEmpty(userGetResponseList)) {
            return;
        }
        // 查询观扫描过该剧场的所有用户
        SouvenirBadgeRequire souvenirBadgeRequire = souvenirBadgeRequireService.findSouvenirBadgeRequire(souvenirBadgeId);
        if (findSouvenirBadgeRequireNoRule(souvenirBadgeRequire)) {
            // 判断是否为指定用户发放
            if (StringUtils.isNotBlank(souvenirBadgeRequire.getWhiteList())) {
                // 指定白名单用户发放
                String[] userIds = souvenirBadgeRequire.getWhiteList().split(",");
                for (String userId : userIds) {
                    if (userPushCollectionService.findUserGet(Long.valueOf(userId), souvenirBadgeId, BadgeTypeFlag.SOUVENIR_BADGE.getCode())) {
                        continue;
                    }
                    userPushCollection = new UserPushCollection();
                    userPushCollection.setMaxNum(issuedQuantity);
                    userPushCollection.setUserId(Long.valueOf(userId));
                    userPushCollection.setTheaterId(theaterId);
                    userPushCollection.setRepertoireId(0L);
                    userPushCollection.setRankMedalId(0L);
                    userPushCollection.setRankMedalInfoId(0L);
                    userPushCollection.setSouvenirBadgeId(souvenirBadgeId);
                    userPushCollection.setStatus(StatusFlag.PROHIBITION.getCode());
                    userPushCollectionList.add(userPushCollection);
                }
            } else {
                for (UserGetResponse user : userGetResponseList) {
                    if (userPushCollectionService.findUserGet(user.getUserId(), souvenirBadgeId, BadgeTypeFlag.SOUVENIR_BADGE.getCode())) {
                        continue;
                    }
                    // 判断观影次数
                    if (souvenirBadgeRequire.getLookNumber() > 0 && user.getNumber() < souvenirBadgeRequire.getLookNumber()) {
                        continue;
                    }
                    RankMedalInfo rankMedalInfo = rankMedalInfoService.getById(souvenirBadgeRequire.getRankMedalInfoId());
                    Long count = userReceivingRecordsService.findUserLookAssignRepertoire(
                            user.getUserId(),
                            souvenirBadge.getTheaterId(),
                            souvenirBadgeRequire.getTimeLookNumber(),
                            rankMedalInfo == null ? 0 : rankMedalInfo.getRankMedalId(),
                            souvenirBadgeRequire.getRankMedalInfoId(),
                            souvenirBadgeRequire.getRepertoireInfoDetailId(),
                            souvenirBadgeRequire.getStartTime(),
                            souvenirBadgeRequire.getEndTime());
                    if (count > 0) {
                        userPushCollection = new UserPushCollection();
                        userPushCollection.setUserId(user.getUserId());
                        userPushCollection.setTheaterId(theaterId);
                        userPushCollection.setRepertoireId(0L);
                        userPushCollection.setRankMedalId(0L);
                        userPushCollection.setRankMedalInfoId(0L);
                        userPushCollection.setSouvenirBadgeId(souvenirBadgeId);
                        userPushCollection.setStatus(StatusFlag.PROHIBITION.getCode());
                        userPushCollectionList.add(userPushCollection);
                    }
                }
            }
        } else {
            // 全部用户发放
            if (CollectionUtils.isNotEmpty(userGetResponseList)) {
                for (UserGetResponse user : userGetResponseList) {
                    if (userPushCollectionService.findUserGet(user.getUserId(), souvenirBadgeId, BadgeTypeFlag.SOUVENIR_BADGE.getCode())) {
                        continue;
                    }
                    userPushCollection = new UserPushCollection();
                    userPushCollection.setUserId(user.getUserId());
                    userPushCollection.setTheaterId(theaterId);
                    userPushCollection.setRepertoireId(0L);
                    userPushCollection.setRankMedalId(0L);
                    userPushCollection.setRankMedalInfoId(0L);
                    userPushCollection.setSouvenirBadgeId(souvenirBadgeId);
                    userPushCollection.setStatus(StatusFlag.PROHIBITION.getCode());
                    userPushCollectionList.add(userPushCollection);
                }
            }
        }
        userPushCollectionService.saveBatch(userPushCollectionList);
    }

    /**
     * 判断是否无规则
     *
     * @param souvenirBadgeRequire
     * @return
     */
    private boolean findSouvenirBadgeRequireNoRule(SouvenirBadgeRequire souvenirBadgeRequire) {
        if (StringUtils.isNotBlank(souvenirBadgeRequire.getWhiteList())) {
            return true;
        }
        if (souvenirBadgeRequire.getRankMedalInfoId() > 0) {
            return true;
        }
        if (souvenirBadgeRequire.getLookNumber() > 0) {
            return true;
        }
        if (souvenirBadgeRequire.getRepertoireInfoDetailId() > 0) {
            return true;
        }
        if (souvenirBadgeRequire.getStartTime() != null && souvenirBadgeRequire.getEndTime() != null) {
            return true;
        }
        return false;
    }
}
