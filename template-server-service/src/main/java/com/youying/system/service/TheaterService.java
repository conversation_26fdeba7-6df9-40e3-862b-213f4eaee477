package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.Theater;
import com.youying.system.domain.common.PullRequest;
import com.youying.system.domain.theater.TheaterRequest;
import com.youying.system.domain.theater.TheaterResponse;

import java.util.List;

/**
 * <p>
 * 剧场表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface TheaterService extends IService<Theater> {

    /**
     * 查询剧场表列表(分页)
     *
     * @param request
     * @return
     */
    List<TheaterResponse> listByPage(TheaterRequest request);

    /**
     * 查询剧场详情
     *
     * @param id
     * @return
     */
    TheaterResponse findTheaterInfo(Long id);

    /**
     * 下拉
     *
     * @param request
     * @return
     */
    List<PullResponse> pull(PullRequest request);

    /**
     * 根据商家ID判断是否添加剧场
     *
     * @param merchantId
     */
    Long findTheaterCountByMerchantId(Long merchantId);

    /**
     * 首页-查询剧场添加数量
     *
     * @param time
     * @return
     */
    Long findTheaterAddCount(TimeRequest time);

    /**
     * 删除剧场
     *
     * @param ids
     * @return
     */
    Integer delete(List<Long> ids);

    /**
     * 修改剧场推荐状态
     *
     * @param id
     * @return
     */
    Long updateRecommend(Long id);
}
