package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.User;
import com.youying.system.domain.common.CountResponse;
import com.youying.system.domain.user.UserRequest;
import com.youying.system.domain.user.UserResponse;

import java.util.List;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface UserService extends IService<User> {
    /**
     * 查询用户表列表(分页)
     *
     * @param request
     * @return
     */
    List<UserResponse> listByPage(UserRequest request);

    /**
     * 添加用户
     *
     * @param user
     * @return
     */
    Long add(User user);

    /**
     * 删除用户
     *
     * @param ids
     * @return
     */
    Integer delete(List<Long> ids);

    /**
     * 首页-查询用户性别数量
     *
     * @param time
     * @return
     */
    List<CountResponse> findUserSexCount(TimeRequest time);
}
