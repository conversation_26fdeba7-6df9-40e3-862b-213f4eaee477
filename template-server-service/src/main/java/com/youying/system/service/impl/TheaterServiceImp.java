package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.Theater;
import com.youying.common.enums.Enums.RecommendFlag;
import com.youying.common.exception.ServiceException;
import com.youying.system.domain.common.PullRequest;
import com.youying.system.domain.theater.TheaterRequest;
import com.youying.system.domain.theater.TheaterResponse;
import com.youying.system.mapper.TheaterMapper;
import com.youying.system.service.RankMedalService;
import com.youying.system.service.RepertoireInfoService;
import com.youying.system.service.RepertoireTicketService;
import com.youying.system.service.SouvenirBadgeService;
import com.youying.system.service.TheaterService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 剧场表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class TheaterServiceImp extends ServiceImpl<TheaterMapper, Theater> implements TheaterService {
    @Autowired
    private RepertoireInfoService repertoireInfoService;
    @Autowired
    private SouvenirBadgeService souvenirBadgeService;
    @Autowired
    private RankMedalService rankMedalService;
    @Autowired
    private RepertoireTicketService repertoireTicketService;

    /**
     * 查询剧场表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<TheaterResponse> listByPage(TheaterRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询剧场详情
     *
     * @param id
     * @return
     */
    @Override
    public TheaterResponse findTheaterInfo(Long id) {
        return baseMapper.findTheaterInfo(id);
    }

    /**
     * 下拉
     *
     * @param request
     * @return
     */
    @Override
    public List<PullResponse> pull(PullRequest request) {
        return baseMapper.pull(request);
    }

    /**
     * 根据商家ID判断是否添加剧场
     *
     * @param merchantId
     * @return
     */
    @Override
    public Long findTheaterCountByMerchantId(Long merchantId) {
        return count(new LambdaQueryWrapper<Theater>()
                .eq(Theater::getMerchantId, merchantId));
    }

    /**
     * 首页-查询剧场添加数量
     *
     * @param time
     * @return
     */
    @Override
    public Long findTheaterAddCount(TimeRequest time) {
        return baseMapper.findTheaterAddCount(time);
    }

    /**
     * 删除剧场
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public Integer delete(List<Long> ids) {
        // 判断剧目是否与剧场关联
        Long count = 0L;
        count = repertoireInfoService.findRepertoireInfoCountByTheaterId(ids);
        if (count > 0) {
            throw new ServiceException("剧场与剧目关联，无法删除");
        }
        // 判断剧场是否存在纪念徽章
        count = souvenirBadgeService.findSouvenirBadgeCountByTheaterId(ids);
        if (count > 0) {
            throw new ServiceException("剧场存在纪念徽章，无法删除");
        }
        // 判断剧场是否存在等级勋章
        count = rankMedalService.findRankMedalCountByTheaterId(ids);
        if (count > 0) {
            throw new ServiceException("剧场存在等级勋章，无法删除");
        }
        // 判断剧场是否存在电子票
        count = repertoireTicketService.findRepertoireTicketByTheaterId(ids);
        if (count > 0) {
            throw new ServiceException("剧场存在电子票，无法删除");
        }
        this.removeBatchByIds(ids);
        repertoireInfoService.batchDeleteByTheaterId(ids);
        souvenirBadgeService.batchDeleteByTheaterId(ids);
        rankMedalService.batchDeleteByTheaterId(ids);
        return ids.size();
    }

    /**
     * 修改剧场推荐状态
     *
     * @param id
     * @return
     */
    @Override
    @Transactional
    public Long updateRecommend(Long id) {
        Theater theater = getById(id);
        if (RecommendFlag.isDefault(theater.getRecommend())) {
            List<Theater> theaterList = list(new LambdaQueryWrapper<Theater>()
                    .eq(Theater::getRecommend, RecommendFlag.RECOMMEND.getCode())
                    .orderByAsc(Theater::getRecommendTime));
            if (CollectionUtils.isNotEmpty(theaterList) && theaterList.size() >= 2) {
                theaterList.get(0).setRecommend(RecommendFlag.DEFAULT.getCode());
                updateById(theaterList.get(0));
            }
            theater.setRecommend(RecommendFlag.RECOMMEND.getCode());
            theater.setRecommendTime(new Date());
        } else {
            theater.setRecommend(RecommendFlag.DEFAULT.getCode());
        }
        updateById(theater);
        return id;
    }
}
