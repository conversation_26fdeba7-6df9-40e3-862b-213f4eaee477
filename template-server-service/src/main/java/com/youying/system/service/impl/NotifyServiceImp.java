package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.MerchantUser;
import com.youying.common.core.domain.entity.Notify;
import com.youying.common.core.domain.entity.User;
import com.youying.common.core.domain.entity.UserNotify;
import com.youying.common.enums.Enums.LookFlag;
import com.youying.common.enums.Enums.PortFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.domain.notify.NotifyRequest;
import com.youying.system.mapper.NotifyMapper;
import com.youying.system.service.MerchantUserService;
import com.youying.system.service.NotifyService;
import com.youying.system.service.UserNotifyService;
import com.youying.system.service.UserService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 通知表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class NotifyServiceImp extends ServiceImpl<NotifyMapper, Notify> implements NotifyService {
    @Autowired
    private UserNotifyService userNotifyService;
    @Autowired
    private UserService userService;
    @Autowired
    private MerchantUserService merchantUserService;

    /**
     * 发送通知
     *
     * @param notify
     * @return
     */
    @Override
    @Transactional
    public Long add(Notify notify) {
        Date nowTime = new Date();
        String username = SecurityUtils.getUsername();
        notify.setCreateTime(nowTime);
        notify.setCreateBy(username);
        notify.setUpdateTime(nowTime);
        notify.setUpdateBy(username);
        save(notify);
        sendNotifyByUser(notify);
        return notify.getId();
    }

    /**
     * 修改通知
     *
     * @param notify
     * @return
     */
    @Override
    @Transactional
    public Long update(Notify notify) {
        if (notify.getPort() == null) {
            notify.setPort(getById(notify.getId()).getPort());
        }
        Notify notifyInfo = getById(notify.getId());
        BeanUtils.copyProperties(notify, notifyInfo);

        updateById(notifyInfo);
        sendNotifyByUser(notifyInfo);
        return notifyInfo.getId();
    }

    /**
     * 推送通知
     */
    @Override
    @Transactional
    public void pushNotify() {
        List<Notify> notifyList = baseMapper.findPushNotify();
        if (CollectionUtils.isNotEmpty(notifyList)) {
            notifyList.forEach(item -> item.setStatus(StatusFlag.OK.getCode()));
            for (Notify notify : notifyList) {
                sendNotifyByUser(notify);
            }
        }
    }

    /**
     * 通知列表
     *
     * @param request
     * @return
     */
    @Override
    public List<Notify> listByPage(NotifyRequest request) {
        return baseMapper.listByPage(request);
    }

    private void sendNotifyByUser(Notify notify) {
        if (!StatusFlag.OK.getCode().equals(notify.getStatus())) {
            return;
        }

        notify.setStatus(StatusFlag.OK.getCode());
        notify.setPushPassTime(new Date());
        baseMapper.updateNotifyById(notify);

        List<UserNotify> userNotifyList = new ArrayList<>();
        String createBy = notify.getCreateBy();
        Integer port = notify.getPort();
        Long notifyId = notify.getId();
        Date nowTime = new Date();

        if (PortFlag.USER.getCode() + PortFlag.MERCHANT.getCode() == port) {
            List<User> userList = userService.list();
            List<MerchantUser> merchantUserList = merchantUserService.list();

            for (User user : userList) {
                UserNotify userNotify = createUserNotify(user.getId(), notifyId, PortFlag.USER.getCode(), createBy, nowTime);
                userNotifyList.add(userNotify);
            }
            for (MerchantUser user : merchantUserList) {
                UserNotify userNotify = createUserNotify(user.getId(), notifyId, PortFlag.MERCHANT.getCode(), createBy, nowTime);
                userNotifyList.add(userNotify);
            }

            if (CollectionUtils.isNotEmpty(userNotifyList)) {
                userNotifyService.insertBatchNotifyInfo(userNotifyList);
            }

            return;
        }

        if (PortFlag.USER.getCode().equals(port)) {
            List<User> userList = userService.list();

            for (User user : userList) {
                UserNotify userNotify = createUserNotify(user.getId(), notifyId, PortFlag.USER.getCode(), createBy, nowTime);
                userNotifyList.add(userNotify);
            }

            if (CollectionUtils.isNotEmpty(userNotifyList)) {
                userNotifyService.insertBatchNotifyInfo(userNotifyList);
            }

            return;
        }

        if (PortFlag.MERCHANT.getCode().equals(port)) {
            List<MerchantUser> merchantUserList = merchantUserService.list();

            for (MerchantUser user : merchantUserList) {
                UserNotify userNotify = createUserNotify(user.getId(), notifyId, PortFlag.MERCHANT.getCode(), createBy, nowTime);
                userNotifyList.add(userNotify);
            }

            if (CollectionUtils.isNotEmpty(userNotifyList)) {
                userNotifyService.insertBatchNotifyInfo(userNotifyList);
            }

            return;
        }

    }

    private UserNotify createUserNotify(Long userId, Long notifyId, Integer port, String createBy, Date notTime) {
        UserNotify userNotify = new UserNotify();
        userNotify.setUserId(userId);
        userNotify.setNotifyId(notifyId);
        userNotify.setPort(port);
        userNotify.setCreateBy(createBy);
        userNotify.setCreateTime(notTime);
        userNotify.setLookFlag(LookFlag.DEFAULT.getCode());
        return userNotify;
    }
}
