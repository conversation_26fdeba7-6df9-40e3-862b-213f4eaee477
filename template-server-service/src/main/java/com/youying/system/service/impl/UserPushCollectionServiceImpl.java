package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserPushCollection;
import com.youying.common.enums.Enums.BadgeTypeFlag;
import com.youying.system.mapper.UserPushCollectionMapper;
import com.youying.system.service.UserPushCollectionService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户推送藏品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-22
 */
@Service
public class UserPushCollectionServiceImpl extends ServiceImpl<UserPushCollectionMapper, UserPushCollection> implements UserPushCollectionService {

    /**
     * 查询是否推送过等级勋章、纪念勋章
     *
     * @param userId
     * @param relationId
     * @param code
     * @return
     */
    @Override
    public boolean findUserGet(Long userId, Long relationId, Integer code) {
        if (BadgeTypeFlag.RANK_MEDAL.getCode().equals(code)) {
            return count(new LambdaQueryWrapper<UserPushCollection>()
                    .eq(UserPushCollection::getUserId, userId)
                    .eq(UserPushCollection::getRankMedalInfoId, relationId)) > 0;
        }
        if (BadgeTypeFlag.SOUVENIR_BADGE.getCode().equals(code)) {
            return count(new LambdaQueryWrapper<UserPushCollection>()
                    .eq(UserPushCollection::getUserId, userId)
                    .eq(UserPushCollection::getSouvenirBadgeId, relationId)) > 0;
        }
        return false;
    }
}
