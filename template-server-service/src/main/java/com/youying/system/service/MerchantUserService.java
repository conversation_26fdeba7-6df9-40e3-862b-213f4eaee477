package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.MerchantUser;

import java.util.List;

/**
 * <p>
 * 商家员工表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface MerchantUserService extends IService<MerchantUser> {

    /**
     * 修改商家员工密码
     *
     * @param merchantUser
     * @return
     */
    Long updatePwd(MerchantUser merchantUser);

    /**
     * 删除商家用户
     *
     * @param ids
     */
    void deleteByMerchantId(List<Long> ids);

    /**
     * 判断登录账号是否被使用
     *
     * @param account
     * @param merchantCategory
     * @return
     */
    MerchantUser findMerchantUserExist(String account, Integer merchantCategory);
}
