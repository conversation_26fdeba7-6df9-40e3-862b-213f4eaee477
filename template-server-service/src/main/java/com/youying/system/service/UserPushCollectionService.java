package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserPushCollection;

/**
 * <p>
 * 用户推送藏品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-22
 */
public interface UserPushCollectionService extends IService<UserPushCollection> {

    /**
     * 查询是否推送过等级勋章、纪念勋章
     *
     * @param userId
     * @param relationId
     * @param code
     * @return
     */
    boolean findUserGet(Long userId, Long relationId, Integer code);
}
