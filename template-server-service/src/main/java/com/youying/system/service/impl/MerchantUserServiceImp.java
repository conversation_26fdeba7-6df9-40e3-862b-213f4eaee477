package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.MerchantUser;
import com.youying.common.utils.SecurityUtils;
import com.youying.system.mapper.MerchantUserMapper;
import com.youying.system.service.MerchantUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 商家员工表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class MerchantUserServiceImp extends ServiceImpl<MerchantUserMapper, MerchantUser> implements MerchantUserService {

    /**
     * 修改商家员工密码
     *
     * @param merchantUser
     * @return
     */
    @Override
    public Long updatePwd(MerchantUser merchantUser) {
        MerchantUser user = getById(merchantUser.getId());
        String newPwd = SecurityUtils.encryptPassword(merchantUser.getPassword());
        user.setPassword(newPwd);
        updateById(user);
        return merchantUser.getId();
    }

    /**
     * 删除商家用户
     *
     * @param ids
     */
    @Override
    @Transactional
    public void deleteByMerchantId(List<Long> ids) {
        remove(new LambdaQueryWrapper<MerchantUser>()
                .in(MerchantUser::getMerchantId, ids));
    }

    /**
     * 判断登录账号是否被使用
     *
     * @param account
     * @param merchantCategory
     * @return
     */
    @Override
    public MerchantUser findMerchantUserExist(String account, Integer merchantCategory) {
        return getOne(new LambdaQueryWrapper<MerchantUser>()
                .eq(MerchantUser::getPhone, account)
                .eq(MerchantUser::getMerchantCategory, merchantCategory));
    }
}
