package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Leaderboard;
import com.youying.common.core.page.PageDomain;
import com.youying.system.domain.leaderboard.LeaderboardRequest;
import com.youying.system.domain.userleaderboard.LeaderboardResponse;
import com.youying.system.mapper.LeaderboardMapper;
import com.youying.system.service.LeaderboardService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 榜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Service
public class LeaderboardServiceImpl extends ServiceImpl<LeaderboardMapper, Leaderboard> implements LeaderboardService {

    /**
     * 查询系统榜单
     *
     * @param pageDomain
     * @return
     */
    @Override
    public List<Leaderboard> listByPage(PageDomain pageDomain) {
        return baseMapper.listByPage(pageDomain);
    }

    /**
     * 榜单剧目统计
     *
     * @param request
     * @return
     */
    @Override
    public List<LeaderboardResponse> findRepertoireLeaderboard(LeaderboardRequest request) {
        return baseMapper.findRepertoireLeaderboard(request);
    }
}
