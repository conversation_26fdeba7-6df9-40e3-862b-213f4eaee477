package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.Merchant;
import com.youying.system.domain.common.PullRequest;
import com.youying.system.domain.merchant.MerchantRequest;
import com.youying.system.domain.merchant.MerchantResponse;

import java.util.List;

/**
 * <p>
 * 商家表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface MerchantService extends IService<Merchant> {

    /**
     * 查询商家表列表(分页)
     *
     * @param request
     * @return
     */
    List<MerchantResponse> listByPage(MerchantRequest request);

    /**
     * 查询商家是否存在
     *
     * @param id
     * @param account
     * @param merchantCategory
     * @return
     */
    Boolean findMerchantExist(Long id, String account, Integer merchantCategory);

    /**
     * 添加商家
     *
     * @param merchant
     * @return
     */
    Long add(Merchant merchant);

    /**
     * 下拉
     *
     * @param request
     * @return
     */
    List<PullResponse> pull(PullRequest request);

    /**
     * 删除商家
     *
     * @param ids
     * @return
     */
    Integer delete(List<Long> ids);

    /**
     * 首页-查询商家增量
     *
     * @param time
     * @return
     */
    Long findMerchantAddCount(TimeRequest time);
}
