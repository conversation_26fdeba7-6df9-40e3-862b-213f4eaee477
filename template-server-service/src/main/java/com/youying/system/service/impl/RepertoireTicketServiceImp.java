package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.exception.ServiceException;
import com.youying.system.domain.repertoireticket.RepertoireTicketRequest;
import com.youying.system.domain.repertoireticket.RepertoireTicketResponse;
import com.youying.system.mapper.RepertoireTicketMapper;
import com.youying.system.service.RepertoireTicketService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 剧目电子票表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class RepertoireTicketServiceImp extends ServiceImpl<RepertoireTicketMapper, RepertoireTicket> implements RepertoireTicketService {

    /**
     * 查询剧目电子票表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<RepertoireTicketResponse> listByPage(RepertoireTicketRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 删除电子票
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public Integer delete(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            for (Long id : ids) {
                RepertoireTicket repertoireTicket = getById(id);
                if (repertoireTicket != null && AuditFlag.PASS.getCode().equals(repertoireTicket.getAudit())) {
                    throw new ServiceException("电子票已审核通过，无法删除");
                }
            }
            removeBatchByIds(ids);
        }
        return ids.size();
    }

    /**
     * 查询电子票关联条数
     *
     * @param repertoireId
     * @param theaterId
     * @return
     */
    @Override
    public Long findRepertoireTicketCount(Long repertoireId, Long theaterId) {
        if (repertoireId != null) {
            return count(new LambdaQueryWrapper<RepertoireTicket>()
                    .eq(RepertoireTicket::getRepertoireId, repertoireId));
        } else {
            return count(new LambdaQueryWrapper<RepertoireTicket>()
                    .eq(RepertoireTicket::getTheaterId, theaterId));
        }
    }

    /**
     * 根据商家ID判断是否添加电子票
     *
     * @param merchantId
     * @return
     */
    @Override
    public Long findRepertoireTicketCountByMerchantId(Long merchantId) {
        return count(new LambdaQueryWrapper<RepertoireTicket>()
                .eq(RepertoireTicket::getMerchantId, merchantId));
    }

    /**
     * 根据剧目ID查询电子票
     *
     * @param theaterId
     * @return
     */
    @Override
    public Long findRepertoireTicketByTheaterId(List<Long> theaterId) {
        return baseMapper.selectCount(new LambdaQueryWrapper<RepertoireTicket>()
                .in(RepertoireTicket::getTheaterId, theaterId)
                .eq(RepertoireTicket::getAudit, AuditFlag.PASS.getCode()));
    }
}
