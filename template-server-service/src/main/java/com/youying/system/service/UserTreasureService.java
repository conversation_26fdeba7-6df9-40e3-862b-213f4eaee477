package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.UserTreasure;
import com.youying.system.domain.common.CountResponse;
import com.youying.system.domain.usertreasure.UserTreasureEx;
import com.youying.system.domain.usertreasure.UserTreasureRequest;
import com.youying.system.domain.usertreasure.UserTreasureResponse;

import java.util.List;

/**
 * <p>
 * 用户剧目剧场收藏表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface UserTreasureService extends IService<UserTreasure> {
    /**
     * 首页-查询剧目剧场关注人数
     *
     * @param time
     * @return
     */
    List<UserTreasureResponse> findUserTreasureAddCount(TimeRequest time);

    /**
     * 首页-查询剧目关注人数
     *
     * @param time
     * @return
     */
    List<CountResponse> findUserTreasureRepertoireCount(TimeRequest time);

    /**
     * 首页-查询剧场关注人数
     *
     * @param time
     * @return
     */
    List<CountResponse> findUserTreasureTheaterCount(TimeRequest time);

    /**
     * 查询关注用户统计(剧目)
     *
     * @param request
     * @return
     */
    List<UserTreasureResponse> findRepertoireFans(UserTreasureRequest request);

    /**
     * 查询关注用户统计(剧目)
     *
     * @param request
     * @return
     */
    List<UserTreasureResponse> findTheaterFans(UserTreasureRequest request);

    /**
     * 导出关注用户统计(剧目)
     *
     * @param request
     * @return
     */
    List<UserTreasureEx> exportRepertoireFans(UserTreasureRequest request);

    /**
     * 导出关注用户统计(剧场)
     *
     * @param request
     * @return
     */
    List<UserTreasureEx> exportTheaterFans(UserTreasureRequest request);
}
