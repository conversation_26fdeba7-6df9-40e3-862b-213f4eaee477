package com.youying.system.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.common.utils.eticket.ETicketCompositeUtil;
import com.youying.common.utils.eticket.TicketInfo;
import com.youying.common.utils.eticket.UserInfo;
import com.youying.common.utils.file.FileUploadUtils;
import com.youying.system.domain.userreceivingrecords.UserGetResponse;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsEx;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsRequest;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsResponse;
import com.youying.system.mapper.UserReceivingRecordsMapper;
import com.youying.system.service.UserReceivingRecordsService;

/**
 * <p>
 * 用户数字藏品领取记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class UserReceivingRecordsServiceImp extends ServiceImpl<UserReceivingRecordsMapper, UserReceivingRecords>
        implements UserReceivingRecordsService {

    @Autowired
    private UserReceivingRecordsMapper userReceivingRecordsMapper;

    /**
     * 查询用户领取数字藏品
     *
     * @param request
     * @return
     */
    @Override
    public List<UserReceivingRecordsResponse> listByPage(UserReceivingRecordsRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询藏品领取详情
     *
     * @param request
     * @return
     */
    @Override
    public List<UserReceivingRecordsResponse> findCollectionRecipient(UserReceivingRecordsRequest request) {
        return baseMapper.findCollectionRecipient(request);
    }

    /**
     * 首页-查询用户领取数字藏品数
     *
     * @param time
     * @param badgeType
     * @return
     */
    @Override
    public Long findUserReceivingRecordsAddCount(TimeRequest time, Integer badgeType) {
        return baseMapper.findUserReceivingRecordsAddCount(time, badgeType);
    }

    /**
     * 导出消费记录
     *
     * @param request
     * @return
     */
    @Override
    public List<UserReceivingRecordsEx> export(UserReceivingRecordsRequest request) {
        return baseMapper.export(request);
    }

    /**
     * 查询藏品领取条数
     *
     * @param ids 藏品组合ID
     * @return
     */
    @Override
    public Long findPortfolioCountById(Long[] ids) {
        return count(new LambdaQueryWrapper<UserReceivingRecords>()
                .in(UserReceivingRecords::getPortfolioId, ids));
    }

    /**
     * 查询用户观影剧目、剧场次数
     *
     * @param relationId
     * @param code
     */
    @Override
    public List<UserGetResponse> findUserLookCount(Long relationId, Integer code) {
        return baseMapper.findUserLookCount(relationId, code);
    }

    /**
     * 查询用户是否观看过指定场次
     *
     * @param userId
     * @param theaterId
     * @param lookNumber
     * @param rankMedalId
     * @param rankMedalInfoId
     * @param repertoireInfoId
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Long findUserLookAssignRepertoire(Long userId,
            Long theaterId,
            Integer lookNumber,
            Long rankMedalId,
            Long rankMedalInfoId,
            Long repertoireInfoId,
            Date startTime,
            Date endTime) {
        return baseMapper.findUserLookAssignRepertoire(userId, theaterId, lookNumber, rankMedalId, rankMedalInfoId,
                repertoireInfoId, startTime, endTime);
    }

    /**
     * 重新生成藏品图片
     * 
     * @param id
     */
    @Override
    public void regenerateUserPortfolioImage(Long portfolioId) {
        List<UserReceivingRecords> userReceivingRecordsList = baseMapper.selectList(
                new LambdaQueryWrapper<UserReceivingRecords>().eq(UserReceivingRecords::getPortfolioId, portfolioId));
        for (UserReceivingRecords userReceivingRecords : userReceivingRecordsList) {
            regenerateUserETicketImage(userReceivingRecords.getId());
        }
    }

    /**
     * 重新生成电子票
     */
    @Override
    public void regenerateUserETicketImage(Long id) {
        UserReceivingRecords userReceivingRecords = getById(id);

        if (userReceivingRecords != null) {

            Map<String, Object> map = userReceivingRecordsMapper.findRegerateUserETicketImageInfo(id);

            TicketInfo ticketInfo = new TicketInfo();

            ticketInfo.setCoverFrontUrl(map.get("eticketBackImage").toString());
            ticketInfo.setRepertoireCoverPictureUrl(map.get("repertoireCoverPicture").toString());
            ticketInfo.setRepertoire(map.get("repertoireName").toString());
            ticketInfo.setSerialNumber(map.get("no").toString());
            ticketInfo.setDateTime(map.get("playTime").toString());
            ticketInfo.setSeat(map.get("seatNumber").toString());

            UserInfo userInfo = new UserInfo();
            userInfo.setAvatarUrl(map.get("avatar").toString());
            userInfo.setName(map.get("name").toString());

            String currentEticketImage = map.get("currentEticketImage").toString();
            String outputPath = FileUploadUtils.getDefaultBaseDir() + currentEticketImage.replace("/profile", "");

            // 生成普通电子票
            ETicketCompositeUtil.generateETicket(ticketInfo, userInfo, outputPath);
        }
    }

}
