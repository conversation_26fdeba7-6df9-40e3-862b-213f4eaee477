package com.youying.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.domain.entity.Scanning;
import com.youying.common.core.domain.entity.ScanningInfo;
import com.youying.common.enums.Enums.DefaultFlag;
import com.youying.common.exception.ServiceException;
import com.youying.system.domain.scanning.AddScanningRequest;
import com.youying.system.domain.scanning.ScanningRequest;
import com.youying.system.domain.scanning.ScanningResponse;
import com.youying.system.mapper.ScanningMapper;
import com.youying.system.service.ScanningInfoService;
import com.youying.system.service.ScanningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Service
public class ScanningServiceImpl extends ServiceImpl<ScanningMapper, Scanning> implements ScanningService {
    @Autowired
    private ScanningInfoService scanningInfoService;

    /**
     * 添加纸质票扫码规则
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public Long add(AddScanningRequest request) {
        Scanning scanning = new Scanning();
        BeanUtil.copyProperties(request, scanning);
        scanning.setDefaultFlag(DefaultFlag.SPECIAL.getCode());
        save(scanning);

        Long scanningId = scanning.getId();
        List<ScanningInfo> scanningInfoList = request.getScanningInfoList();

        for (ScanningInfo scanningInfo : scanningInfoList) {
            scanningInfo.setScanningId(scanningId);
        }

        scanningInfoService.saveBatch(scanningInfoList);

        return scanningId;
    }

    /**
     * 修改纸质票扫码规则
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public Long update(AddScanningRequest request) {
        Long scanningId = request.getId();
        Scanning scanning = getById(scanningId);
        scanning.setName(request.getName());
        if (DefaultFlag.DEFAULT.getCode().equals(scanning.getDefaultFlag())) {
            scanning.setStatus(request.getStatus());
        }
        updateById(scanning);

        // 删除纸质票
        scanningInfoService.deleteByScanningId(scanningId);

        List<ScanningInfo> scanningInfoList = request.getScanningInfoList();
        for (ScanningInfo scanningInfo : scanningInfoList) {
            scanningInfo.setScanningId(scanningId);
        }
        scanningInfoService.saveBatch(scanningInfoList);
        return scanningId;
    }

    /**
     * 删除纸质票规则
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public Long delete(Long[] ids) {
        // 判断是否存在默认规则
        long count = count(new LambdaQueryWrapper<Scanning>()
                .eq(Scanning::getDefaultFlag, DefaultFlag.DEFAULT.getCode())
                .in(Scanning::getId, ids));
        if (count > 0) {
            throw new ServiceException("无法删除默认规则");
        }
        removeBatchByIds(Arrays.asList(ids));
        scanningInfoService.deleteByScanningIds(ids);
        return count;
    }

    /**
     * 查询纸质票扫描规则
     *
     * @param request
     * @return
     */
    @Override
    public List<ScanningResponse> listByPage(ScanningRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 纸质票扫描规则详情
     *
     * @param id
     * @return
     */
    @Override
    public ScanningResponse details(Long id) {
        return baseMapper.details(id);
    }

    /**
     * 下拉
     *
     * @return
     */
    @Override
    public List<PullResponse> pull() {
        return baseMapper.pull();
    }
}
