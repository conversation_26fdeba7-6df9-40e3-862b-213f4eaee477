package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.SouvenirBadge;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeRequest;
import com.youying.system.domain.souvenirbadge.SouvenirBadgeResponse;

import java.util.List;

/**
 * <p>
 * 剧场纪念徽章表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface SouvenirBadgeService extends IService<SouvenirBadge> {

    /**
     * 查询剧场纪念徽章表列表(分页)
     *
     * @param request
     * @return
     */
    List<SouvenirBadgeResponse> listByPage(SouvenirBadgeRequest request);

    /**
     * 删除剧场纪念徽章
     *
     * @param ids
     * @return
     */
    Integer delete(List<Long> ids);

    /**
     * 根据商家ID判断是否添加纪念徽章
     *
     * @param merchantId
     * @return
     */
    Long findSouvenirBadgeCountByMerchantId(Long merchantId);

    /**
     * 根据剧场ID查询纪念徽章
     *
     * @param theaterId
     * @return
     */
    Long findSouvenirBadgeCountByTheaterId(List<Long> theaterId);

    /**
     * 根据剧场ID批量删除剧场纪念徽章
     *
     * @param theaterId
     */
    void batchDeleteByTheaterId(List<Long> theaterId);

    /**
     * 发送纪念徽章
     *
     * @param souvenirBadge
     */
    void sendSouvenirBadge(SouvenirBadge souvenirBadge);
}
