package com.youying.system.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.ImageScanRecord;
import com.youying.common.core.page.PageDomain;
import com.youying.system.domain.imagescanrecord.ImageScanRecordResponse;
import com.youying.system.mapper.ImageScanRecordMapper;
import com.youying.system.service.ImageScanRecordService;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Service
public class ImageScanRecordServiceImpl extends ServiceImpl<ImageScanRecordMapper, ImageScanRecord>
        implements ImageScanRecordService {

    /**
     * 查询纸质票扫描记录
     *
     * @param pageDomain
     * @return
     */
    @Override
    public List<ImageScanRecordResponse> list(PageDomain pageDomain) {
        return baseMapper.list(pageDomain);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public ImageScanRecordResponse details(Long id) {
        return baseMapper.details(id);
    }
}
