package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.DigitalAvatarBlockchain;
import com.youying.system.mapper.DigitalAvatarBlockchainMapper;
import com.youying.system.service.DigitalAvatarBlockchainService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 电子头像区块链表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-21
 */
@Service
public class DigitalAvatarBlockchainServiceImpl extends ServiceImpl<DigitalAvatarBlockchainMapper, DigitalAvatarBlockchain> implements DigitalAvatarBlockchainService {

    /**
     * 查询数字头像是否添加
     *
     * @param digitalAvatarId
     * @return
     */
    @Override
    public Long findDigitalAvatarBlockchainCount(Long digitalAvatarId, Integer status) {
        return count(new LambdaQueryWrapper<DigitalAvatarBlockchain>()
                .eq(DigitalAvatarBlockchain::getDigitalAvatarId, digitalAvatarId)
                .eq(DigitalAvatarBlockchain::getStatus, status));
    }

    /**
     * 查询数字头像
     *
     * @param digitalAvatarId
     * @param status
     * @return
     */
    @Override
    public List<DigitalAvatarBlockchain> findDigitalAvatarBlock(Long digitalAvatarId, Integer status) {
        return list(new LambdaQueryWrapper<DigitalAvatarBlockchain>()
                .eq(DigitalAvatarBlockchain::getDigitalAvatarId, digitalAvatarId)
                .eq(DigitalAvatarBlockchain::getStatus, status));
    }
}
