package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Issue;
import com.youying.system.domain.issue.IssueRequest;
import com.youying.system.domain.issue.IssueResponse;
import com.youying.system.mapper.IssueMapper;
import com.youying.system.service.IssueService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 剧目剧场问答表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class IssueServiceImp extends ServiceImpl<IssueMapper, Issue> implements IssueService {

    /**
     * 查询剧目剧场问答表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<IssueResponse> listByPage(IssueRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询剧目剧场问答表详情
     *
     * @param id
     * @return
     */
    @Override
    public IssueResponse details(Long id) {
        return baseMapper.details(id);
    }
}
