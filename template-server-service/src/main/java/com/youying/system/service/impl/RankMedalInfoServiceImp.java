package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.RankMedalInfo;
import com.youying.system.mapper.RankMedalInfoMapper;
import com.youying.system.service.RankMedalInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 等级勋章详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class RankMedalInfoServiceImp extends ServiceImpl<RankMedalInfoMapper, RankMedalInfo> implements RankMedalInfoService {

    /**
     * 删除等级勋章详情
     *
     * @param rankMedalId
     */
    @Override
    @Transactional
    public void deleteByRankMedalId(Long rankMedalId) {
        remove(new LambdaQueryWrapper<RankMedalInfo>()
                .eq(RankMedalInfo::getRankMedalId, rankMedalId));
    }

    /**
     * 删除等级勋章详情
     *
     * @param rankMedalIds
     */
    @Override
    @Transactional
    public void deleteByRankMedalId(List<Long> rankMedalIds) {
        remove(new LambdaQueryWrapper<RankMedalInfo>()
                .in(RankMedalInfo::getRankMedalId, rankMedalIds));
    }

    /**
     * 查询等级勋章详情
     *
     * @param rankMedalId
     */
    @Override
    public List<RankMedalInfo> findRankMedalInfo(Long rankMedalId) {
        return list(new LambdaQueryWrapper<RankMedalInfo>()
                .eq(RankMedalInfo::getRankMedalId, rankMedalId));
    }
}
