package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.RepertoireLabel;
import com.youying.system.domain.repertoire.AddRepertoireLabelRequest;
import com.youying.system.domain.repertoirelabel.RepertoireLabelRequest;
import com.youying.system.domain.repertoirelabel.RepertoireLabelResponse;

import java.util.List;

/**
 * <p>
 * 剧目标签表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RepertoireLabelService extends IService<RepertoireLabel> {
    /**
     * 修改剧目标签
     *
     * @param request
     * @return
     */
    Long updateLabel(AddRepertoireLabelRequest request);

    /**
     * 查询剧目标签表列表(分页)
     *
     * @param request
     * @return
     */
    List<RepertoireLabelResponse> listByPage(RepertoireLabelRequest request);
}
