package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.UserOrder;
import com.youying.system.domain.userorder.UserOrderEx;
import com.youying.system.domain.userorder.UserOrderRequest;
import com.youying.system.domain.userorder.UserOrderResponse;
import com.youying.system.mapper.UserOrderMapper;
import com.youying.system.service.UserOrderService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 用户订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Service
public class UserOrderServiceImpl extends ServiceImpl<UserOrderMapper, UserOrder> implements UserOrderService {

    /**
     * 用户订单列表
     *
     * @param request
     * @return
     */
    @Override
    public List<UserOrderResponse> listByPage(UserOrderRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public UserOrderResponse details(Long id) {
        return baseMapper.details(id);
    }

    /**
     * 查询商家订单总价
     *
     * @param time
     * @return
     */
    @Override
    public BigDecimal findOrderSumPrice(TimeRequest time) {
        return baseMapper.findOrderSumPrice(time);
    }

    /**
     * 导出订单信息
     *
     * @param request
     * @return
     */
    @Override
    public List<UserOrderEx> export(UserOrderRequest request) {
        return baseMapper.export(request);
    }
}
