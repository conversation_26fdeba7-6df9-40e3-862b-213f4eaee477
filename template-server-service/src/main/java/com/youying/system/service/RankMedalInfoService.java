package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.RankMedalInfo;

import java.util.List;

/**
 * <p>
 * 等级勋章详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RankMedalInfoService extends IService<RankMedalInfo> {

    /**
     * 删除等级勋章详情
     *
     * @param rankMedalId
     */
    void deleteByRankMedalId(Long rankMedalId);

    /**
     * 删除等级勋章详情
     *
     * @param rankMedalIds
     */
    void deleteByRankMedalId(List<Long> rankMedalIds);

    /**
     * 查询等级勋章详情
     *
     * @param rankMedalId
     */
    List<RankMedalInfo> findRankMedalInfo(Long rankMedalId);
}
