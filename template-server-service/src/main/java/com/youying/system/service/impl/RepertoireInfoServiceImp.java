package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.RepertoireInfo;
import com.youying.common.enums.Enums.RelevanceFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.system.domain.repertoireinfo.RepertoireInfoRequest;
import com.youying.system.domain.repertoireinfo.RepertoireInfoResponse;
import com.youying.system.mapper.RepertoireInfoMapper;
import com.youying.system.service.RepertoireInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 剧目场次信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class RepertoireInfoServiceImp extends ServiceImpl<RepertoireInfoMapper, RepertoireInfo>
        implements RepertoireInfoService {
    /**
     * 查询剧目场次信息表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<RepertoireInfoResponse> listByPage(RepertoireInfoRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 关联修改
     *
     * @param repertoireInfo
     * @return
     */
    @Override
    @Transactional
    public Long updateRelevance(RepertoireInfo repertoireInfo) {
        RepertoireInfo info = getById(repertoireInfo.getId());
        if (RelevanceFlag.CANCEL.getCode().equals(repertoireInfo.getStatus())) {
            info.setStatus(RelevanceFlag.CANCEL.getCode());
            info.setCancelMerchantId(info.getReleaseMerchantId());
            updateById(info);
            return repertoireInfo.getId();
        }
        info.setStatus(RelevanceFlag.PASS.getCode());
        info.setRepertoirePass(RelevanceFlag.PASS.getCode());
        info.setTheaterPass(RelevanceFlag.PASS.getCode());
        info.setCancelMerchantId(0L);
        updateById(info);
        return repertoireInfo.getId();
    }

    /**
     * 查询剧目、剧场关联信息条数
     *
     * @param repertoireId 剧目ID
     * @param theaterId    剧场ID
     * @return
     */
    @Override
    public Long findRepertoireInfoCount(Long repertoireId, Long theaterId) {
        if (repertoireId != null) {
            return count(new LambdaQueryWrapper<RepertoireInfo>()
                    .eq(RepertoireInfo::getRepertoireId, repertoireId)
                    .eq(RepertoireInfo::getStatus, StatusFlag.OK.getCode()));
        } else {
            return count(new LambdaQueryWrapper<RepertoireInfo>()
                    .eq(RepertoireInfo::getTheaterId, theaterId)
                    .eq(RepertoireInfo::getStatus, StatusFlag.OK.getCode()));
        }
    }

    /**
     * 查询剧场关联条数(审核通过)
     *
     * @param theaterId
     * @return
     */
    @Override
    public Long findRepertoireInfoCountByTheaterId(List<Long> theaterId) {
        return baseMapper.selectCount(new LambdaQueryWrapper<RepertoireInfo>()
                .in(RepertoireInfo::getTheaterId, theaterId)
                .eq(RepertoireInfo::getAudit, StatusFlag.OK.getCode()));
    }

    /**
     * 批量删除剧场关联
     *
     * @param ids
     */
    @Override
    @Transactional
    public void batchDeleteByTheaterId(List<Long> ids) {
        baseMapper.delete(new LambdaQueryWrapper<RepertoireInfo>()
                .in(RepertoireInfo::getTheaterId, ids));
    }

}
