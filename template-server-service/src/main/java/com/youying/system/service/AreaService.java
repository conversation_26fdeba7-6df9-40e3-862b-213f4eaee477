package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Area;
import com.youying.system.domain.common.area.Tree;

import java.util.List;

/**
 * <p>
 * 地区表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface AreaService extends IService<Area> {

    /**
     * 查询地区表树形
     *
     * @return
     */
    List<Tree> findAreaTree();

    /**
     * 地区详情
     *
     * @param id
     * @return
     */
    Area details(Long id);
}
