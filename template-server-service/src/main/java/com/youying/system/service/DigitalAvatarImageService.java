package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.DigitalAvatarImage;

import java.util.List;

/**
 * <p>
 * 数字头像图片表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
public interface DigitalAvatarImageService extends IService<DigitalAvatarImage> {

    /**
     * 根据数字头像ID删除图片
     *
     * @param digitalAvatarId
     */
    void deleteByDigitalAvatarId(Long digitalAvatarId);

    /**
     * 根据数字头像查询图片
     *
     * @param digitalAvatarId
     * @return
     */
    List<DigitalAvatarImage> findAvatarImageByDigitalAvatar(Long digitalAvatarId);
}
