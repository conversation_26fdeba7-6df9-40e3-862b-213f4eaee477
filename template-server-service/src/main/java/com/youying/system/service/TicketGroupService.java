package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.TicketGroup;
import com.youying.common.core.page.PageDomain;

import java.util.List;

/**
 * <p>
 * 电子票分组表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
public interface TicketGroupService extends IService<TicketGroup> {

    /**
     * 查询电子票分组
     *
     * @param pageDomain
     * @return
     */
    List<TicketGroup> listByPage(PageDomain pageDomain);
}
