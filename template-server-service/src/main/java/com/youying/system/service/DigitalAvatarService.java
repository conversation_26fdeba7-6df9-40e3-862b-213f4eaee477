package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.DigitalAvatar;
import com.youying.system.domain.common.AuditRequest;
import com.youying.system.domain.digitalavatar.DigitalAvatarRequest;
import com.youying.system.domain.digitalavatar.DigitalAvatarResponse;

import java.util.List;

/**
 * <p>
 * 数字头像表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface DigitalAvatarService extends IService<DigitalAvatar> {

    /**
     * 查询数字头像表列表(分页)
     *
     * @param request
     * @return
     */
    List<DigitalAvatarResponse> listByPage(DigitalAvatarRequest request);

    /**
     * 删除数字头像
     *
     * @param ids
     * @return
     */
    Integer delete(List<Long> ids);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    DigitalAvatarResponse details(Long id);

    /**
     * 查询数字头像与剧目，剧场关联条数
     *
     * @param repertoireId
     * @param theaterId
     * @return
     */
    Long findDigitalAvatarCount(Long repertoireId, Long theaterId);

    /**
     * 根据商家ID判断是否添加电子头像
     *
     * @param merchantId
     * @return
     */
    Long findDigitalAvatarCountByMerchantId(Long merchantId);

    /**
     * 审核数字头像
     *
     * @param request
     * @return
     */
    Long audit(AuditRequest request);

}
