package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Issue;
import com.youying.system.domain.issue.IssueRequest;
import com.youying.system.domain.issue.IssueResponse;

import java.util.List;

/**
 * <p>
 * 剧目剧场问答表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface IssueService extends IService<Issue> {

    /**
     * 查询剧目剧场问答表列表(分页)
     *
     * @param request
     * @return
     */
    List<IssueResponse> listByPage(IssueRequest request);

    /**
     * 查询剧目剧场问答表详情
     *
     * @param id
     * @return
     */
    IssueResponse details(Long id);
}
