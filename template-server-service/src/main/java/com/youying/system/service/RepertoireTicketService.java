package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.system.domain.repertoireticket.RepertoireTicketRequest;
import com.youying.system.domain.repertoireticket.RepertoireTicketResponse;

import java.util.List;

/**
 * <p>
 * 剧目电子票表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RepertoireTicketService extends IService<RepertoireTicket> {

    /**
     * 查询剧目电子票表列表(分页)
     *
     * @param request
     * @return
     */
    List<RepertoireTicketResponse> listByPage(RepertoireTicketRequest request);

    /**
     * 删除电子票
     *
     * @param ids
     * @return
     */
    Integer delete(List<Long> ids);

    /**
     * 查询电子票与剧目、剧场关联条数
     *
     * @param repertoireId
     * @param theaterId
     * @return
     */
    Long findRepertoireTicketCount(Long repertoireId, Long theaterId);

    /**
     * 根据商家ID判断是否添加电子票
     *
     * @param merchantId
     * @return
     */
    Long findRepertoireTicketCountByMerchantId(Long merchantId);

    /**
     * 根据剧目ID查询电子票
     *
     * @param theaterId
     * @return
     */
    Long findRepertoireTicketByTheaterId(List<Long> theaterId);
}
