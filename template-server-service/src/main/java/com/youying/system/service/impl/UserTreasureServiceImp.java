package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.UserTreasure;
import com.youying.system.domain.common.CountResponse;
import com.youying.system.domain.usertreasure.UserTreasureEx;
import com.youying.system.domain.usertreasure.UserTreasureRequest;
import com.youying.system.domain.usertreasure.UserTreasureResponse;
import com.youying.system.mapper.UserTreasureMapper;
import com.youying.system.service.UserTreasureService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户剧目剧场收藏表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class UserTreasureServiceImp extends ServiceImpl<UserTreasureMapper, UserTreasure> implements UserTreasureService {
    /**
     * 首页-查询剧目剧场关注人数
     *
     * @param time
     * @return
     */
    @Override
    public List<UserTreasureResponse> findUserTreasureAddCount(TimeRequest time) {
        return baseMapper.findUserTreasureAddCount(time);
    }

    /**
     * 首页-查询剧场关注人数
     *
     * @param time
     * @return
     */
    @Override
    public List<CountResponse> findUserTreasureRepertoireCount(TimeRequest time) {
        return baseMapper.findUserTreasureRepertoireCount(time);
    }

    /**
     * 首页-查询剧目关注人数
     *
     * @param time
     * @return
     */
    @Override
    public List<CountResponse> findUserTreasureTheaterCount(TimeRequest time) {
        return baseMapper.findUserTreasureTheaterCount(time);
    }

    /**
     * 查询关注用户统计(剧目)
     *
     * @param request
     * @return
     */
    @Override
    public List<UserTreasureResponse> findRepertoireFans(UserTreasureRequest request) {
        return baseMapper.findRepertoireFans(request);
    }

    /**
     * 查询关注用户统计(剧场)
     *
     * @param request
     * @return
     */
    @Override
    public List<UserTreasureResponse> findTheaterFans(UserTreasureRequest request) {
        return baseMapper.findTheaterFans(request);
    }

    /**
     * 导出关注用户统计(剧目)
     *
     * @param request
     * @return
     */
    @Override
    public List<UserTreasureEx> exportRepertoireFans(UserTreasureRequest request) {
        return baseMapper.exportRepertoireFans(request);
    }

    /**
     * 导出关注用户统计(剧场)
     *
     * @param request
     * @return
     */
    @Override
    public List<UserTreasureEx> exportTheaterFans(UserTreasureRequest request) {
        return baseMapper.exportTheaterFans(request);
    }
}
