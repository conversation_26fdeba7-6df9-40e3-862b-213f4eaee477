package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.RankMedal;
import com.youying.common.core.domain.entity.RankMedalInfo;
import com.youying.common.core.domain.entity.UserPushCollection;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.enums.Enums.BadgeTypeFlag;
import com.youying.common.enums.Enums.MerchantCategoryFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.exception.ServiceException;
import com.youying.system.domain.rankmedal.RankMedalRequest;
import com.youying.system.domain.rankmedal.RankMedalResponse;
import com.youying.system.domain.userreceivingrecords.UserGetResponse;
import com.youying.system.mapper.RankMedalMapper;
import com.youying.system.service.RankMedalInfoService;
import com.youying.system.service.RankMedalService;
import com.youying.system.service.UserPushCollectionService;
import com.youying.system.service.UserReceivingRecordsService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 等级勋章表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class RankMedalServiceImp extends ServiceImpl<RankMedalMapper, RankMedal> implements RankMedalService {
    @Autowired
    private RankMedalInfoService rankMedalInfoService;
    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;
    @Autowired
    private UserPushCollectionService userPushCollectionService;

    /**
     * 查询等级勋章表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<RankMedalResponse> listByPage(RankMedalRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 删除等级勋章
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public Integer delete(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            for (Long id : ids) {
                RankMedal rankMedal = getById(id);
                if (rankMedal != null && AuditFlag.PASS.getCode().equals(rankMedal.getAudit())) {
                    throw new ServiceException("等级勋章已审核通过，无法删除");
                }
            }
            // 删除等级勋章详情
            rankMedalInfoService.deleteByRankMedalId(ids);
            removeBatchByIds(ids);
        }
        return ids.size();
    }

    /**
     * 查询等级勋章剧目、剧场关联条数
     *
     * @param repertoireId
     * @param theaterId
     * @return
     */
    @Override
    @Transactional
    public Long findRankMedalCount(Long repertoireId, Long theaterId) {
        if (repertoireId != null) {
            return count(new LambdaQueryWrapper<RankMedal>()
                    .eq(RankMedal::getRepertoireId, repertoireId));
        } else {
            return count(new LambdaQueryWrapper<RankMedal>()
                    .eq(RankMedal::getTheaterId, theaterId));
        }
    }

    /**
     * 查询等级勋章表详情
     *
     * @param id
     * @return
     */
    @Override
    public RankMedalResponse details(Long id) {
        return baseMapper.details(id);
    }

    /**
     * 根据商家ID判断是否添加等级勋章
     *
     * @param merchantId
     * @return
     */
    @Override
    public Long findRankMedalCountByMerchantId(Long merchantId) {
        return count(new LambdaQueryWrapper<RankMedal>()
                .eq(RankMedal::getMerchantId, merchantId));
    }

    /**
     * 查询剧场与等级勋章关联条数
     *
     * @param theaterId
     * @return
     */
    @Override
    public Long findRankMedalCountByTheaterId(List<Long> theaterId) {
        return baseMapper.selectCount(new LambdaQueryWrapper<RankMedal>()
                .in(RankMedal::getTheaterId, theaterId)
                .eq(RankMedal::getAudit, AuditFlag.PASS.getCode()));
    }

    /**
     * 根据剧场ID批量删除剧等级勋章
     *
     * @param ids
     */
    @Override
    @Transactional
    public void batchDeleteByTheaterId(List<Long> ids) {
        baseMapper.delete(new LambdaQueryWrapper<RankMedal>()
                .in(RankMedal::getTheaterId, ids));
    }

    /**
     * 推送等级勋章
     *
     * @param rankMedal
     */
    @Override
    @Transactional
    public void sendRankMedal(RankMedal rankMedal) {
        // 查询用户查看剧目、剧场场次
        Long rankMedalId = rankMedal.getId();

        List<RankMedalInfo> rankMedalInfoList = rankMedalInfoService.findRankMedalInfo(rankMedalId);

        if (CollectionUtils.isNotEmpty(rankMedalInfoList)) {
            Long theaterId = rankMedal.getTheaterId();
            Long repertoireId = rankMedal.getRepertoireId();
            List<UserGetResponse> userGetResponseList = new ArrayList<>();

            if (theaterId != null && theaterId > 0L) {
                userGetResponseList = userReceivingRecordsService.findUserLookCount(theaterId, MerchantCategoryFlag.THEATER.getCode());
            } else {
                userGetResponseList = userReceivingRecordsService.findUserLookCount(repertoireId, MerchantCategoryFlag.REPERTOIRE.getCode());
            }

            if (CollectionUtils.isNotEmpty(userGetResponseList)) {
                List<UserPushCollection> userPushCollectionList = new ArrayList<UserPushCollection>(userGetResponseList.size());
                UserPushCollection userPushCollection = new UserPushCollection();
                for (UserGetResponse user : userGetResponseList) {
                    for (RankMedalInfo rankMedalInfo : rankMedalInfoList) {
                        if (userPushCollectionService.findUserGet(user.getUserId(), rankMedalInfo.getId(), BadgeTypeFlag.RANK_MEDAL.getCode())) {
                            continue;
                        }
                        // 判断是否符合区间要求
                        if (user.getPrice().compareTo(rankMedalInfo.getExpensePrice()) >= 0 && user.getNumber() >= rankMedalInfo.getExpenseNumber()) {
                            userPushCollection = new UserPushCollection();
                            userPushCollection.setUserId(user.getUserId());
                            userPushCollection.setTheaterId(theaterId);
                            userPushCollection.setRepertoireId(repertoireId);
                            userPushCollection.setRankMedalId(rankMedalId);
                            userPushCollection.setRankMedalInfoId(rankMedalInfo.getId());
                            userPushCollection.setSouvenirBadgeId(0L);
                            userPushCollection.setStatus(StatusFlag.PROHIBITION.getCode());
                            userPushCollectionList.add(userPushCollection);
                        }
                    }
                }
                userPushCollectionService.saveBatch(userPushCollectionList);
            }
        }
    }

}
