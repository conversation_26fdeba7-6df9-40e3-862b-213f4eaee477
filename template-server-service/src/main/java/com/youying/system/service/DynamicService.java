package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Dynamic;
import com.youying.system.domain.dynamic.DynamicRequest;
import com.youying.system.domain.dynamic.DynamicResponse;

import java.util.List;

/**
 * <p>
 * 剧目剧场动态表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface DynamicService extends IService<Dynamic> {
    /**
     * 查询剧目剧场动态表列表(分页)
     *
     * @param request
     * @return
     */
    List<DynamicResponse> listByPage(DynamicRequest request);

    /**
     * 查询剧目剧场动态表详情
     *
     * @param id
     * @return
     */
    DynamicResponse details(Long id);

    /**
     * 删除动态
     *
     * @param ids
     * @return
     */
    Integer delete(List<Long> ids);

    /**
     * 修改剧目剧场动态状态
     *
     * @param id
     * @return
     */
    Long updateStatus(Long id);
}
