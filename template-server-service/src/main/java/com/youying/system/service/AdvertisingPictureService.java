package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.AdvertisingPicture;
import com.youying.system.domain.advertisingpicture.AdvertisingPictureRequest;

import java.util.List;

/**
 * <p>
 * 广告轮播图表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface AdvertisingPictureService extends IService<AdvertisingPicture> {
    /**
     * 查询广告轮播图表列表(分页)
     *
     * @param request
     * @return
     */
    List<AdvertisingPicture> listByPage(AdvertisingPictureRequest request);
}
