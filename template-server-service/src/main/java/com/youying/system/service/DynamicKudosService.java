package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.DynamicKudos;

import java.util.List;

/**
 * <p>
 * 剧场动态点赞表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-14
 */
public interface DynamicKudosService extends IService<DynamicKudos> {

    /**
     * 删除动态点赞
     *
     * @param ids
     */
    void deleteByDynamicId(List<Long> ids);
}
