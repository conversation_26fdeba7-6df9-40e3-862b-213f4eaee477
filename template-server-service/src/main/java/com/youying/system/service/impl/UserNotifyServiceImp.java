package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserNotify;
import com.youying.system.mapper.UserNotifyMapper;
import com.youying.system.service.UserNotifyService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户推送信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class UserNotifyServiceImp extends ServiceImpl<UserNotifyMapper, UserNotify> implements UserNotifyService {

    /**
     * 批量添加推送信息
     *
     * @param userNotifyList
     */
    @Override
    public void insertBatchNotifyInfo(List<UserNotify> userNotifyList) {
        baseMapper.insertBatchNotifyInfo(userNotifyList);
    }

    /**
     * 查询推送消息详情
     *
     * @param id
     * @return
     */
    @Override
    public UserNotify details(Long id) {
        return baseMapper.details(id);
    }
}
