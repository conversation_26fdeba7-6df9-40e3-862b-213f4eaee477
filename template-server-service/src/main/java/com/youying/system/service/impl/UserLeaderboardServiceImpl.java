package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserLeaderboard;
import com.youying.system.domain.user.UserRequest;
import com.youying.system.domain.userleaderboard.UserLeaderboardResponse;
import com.youying.system.mapper.UserLeaderboardMapper;
import com.youying.system.service.UserLeaderboardService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户排行榜表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Service
public class UserLeaderboardServiceImpl extends ServiceImpl<UserLeaderboardMapper, UserLeaderboard> implements UserLeaderboardService {

    /**
     * 查询用户排行榜表
     *
     * @param request
     * @return
     */
    @Override
    public List<UserLeaderboardResponse> findUserLeaderboard(UserRequest request) {
        return baseMapper.findUserLeaderboard(request);
    }
}
