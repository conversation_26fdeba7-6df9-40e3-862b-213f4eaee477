package com.youying.system.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.DigitalAvatar;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.common.core.domain.entity.PortfolioInfo;
import com.youying.common.core.domain.entity.RepertoireTicket;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.StringUtils;
import com.youying.system.domain.portfolio.PortfolioRequest;
import com.youying.system.domain.portfolio.PortfolioResponse;
import com.youying.system.mapper.PortfolioMapper;
import com.youying.system.service.DigitalAvatarService;
import com.youying.system.service.PortfolioInfoService;
import com.youying.system.service.PortfolioService;
import com.youying.system.service.RepertoireTicketService;
import com.youying.system.service.UserReceivingRecordsService;

/**
 * <p>
 * 藏品组合表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Service
public class PortfolioServiceImpl extends ServiceImpl<PortfolioMapper, Portfolio> implements PortfolioService {
    @Autowired
    private UserReceivingRecordsService userReceivingRecordsService;
    @Autowired
    private PortfolioInfoService portfolioInfoService;
    @Autowired
    private RepertoireTicketService repertoireTicketService;
    @Autowired
    private DigitalAvatarService digitalAvatarService;

    /**
     * 藏品组合表列表
     *
     * @param request
     * @return
     */
    @Override
    public List<PortfolioResponse> listByPage(PortfolioRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 删除藏品组合
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public Integer delete(Long[] ids) {
        // 判断是否
        Long count = userReceivingRecordsService.findPortfolioCountById(ids);
        if (count > 0) {
            throw new ServiceException("已有用户领取，无法删除");
        }
        removeBatchByIds(Arrays.asList(ids));
        return ids.length;
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public PortfolioResponse details(Long id) {
        return baseMapper.details(id);
    }

    /**
     * 商品审核
     *
     * @param portfolio
     * @return
     */
    @Override
    @Transactional
    public Long audit(Portfolio portfolio) {
        Portfolio portfolioInfo = getById(portfolio.getId());
        if (AuditFlag.PASS.getCode().equals(portfolio.getAudit())) {
            portfolioInfo.setAudit(AuditFlag.PASS.getCode());
            portfolioInfo.setAuditFlag(AuditFlag.PASS.getCode());
            portfolioInfo.setAuditPassTime(new Date());

            DigitalAvatar digitalAvatar = digitalAvatarService.getById(portfolioInfo.getDigitalAvatarId());
            RepertoireTicket repertoireTicket = repertoireTicketService.getById(portfolioInfo.getRepertoireTicketId());

            // 添加商品审核记录
            portfolioInfoService.updateStatus(portfolioInfo.getId());

            // 添加商品审核记录
            PortfolioInfo portfolioInfoDetail = new PortfolioInfo();
            BeanUtils.copyProperties(portfolioInfo, portfolioInfoDetail);
            portfolioInfoDetail.setId(null);
            portfolioInfoDetail.setPortfolioId(portfolioInfo.getId());
            if (repertoireTicket != null) {
                portfolioInfoDetail.setCoverFront(repertoireTicket.getCoverFront());
                portfolioInfoDetail.setCoverReverse(repertoireTicket.getCoverReverse());
                portfolioInfoDetail.setCommonImage(repertoireTicket.getCommonImage());

            }
            if (digitalAvatar != null) {
                if (StringUtils.isNotBlank(repertoireTicket.getCoverFront())
                        && digitalAvatar.getMaxQuantity() < portfolioInfo.getIssuedQuantity() - 1) {
                    throw new ServiceException("发放数量超出数字头像阈值");
                }

                portfolioInfoDetail.setDigitalAvatarCommonImage(digitalAvatar.getCommonImage());
            }
            portfolioInfoDetail.setStatus(StatusFlag.OK.getCode());
            portfolioInfoService.save(portfolioInfoDetail);

            // 如果更新了电子票图片，重新生成藏品图片
            if (repertoireTicket != null && StringUtils.isNotBlank(repertoireTicket.getCommonImage())) {
                // 异步线程处理
                new Thread(() -> {
                    userReceivingRecordsService.regenerateUserPortfolioImage(portfolioInfo.getId());
                }).start();
            }
        } else {
            // 驳回
            portfolioInfo.setReasonsRejection(portfolio.getReasonsRejection());
            portfolioInfo.setAudit(AuditFlag.REJECTED.getCode());
        }
        portfolioInfo.setPrice(portfolio.getPrice());
        portfolioInfo.setFree(portfolio.getFree());
        portfolioInfo.setPortfolioStatementId(portfolio.getPortfolioStatementId());
        portfolioInfo.setStatement(portfolio.getStatement());

        updateById(portfolioInfo);

        return portfolio.getId();
    }

    /**
     * 修改商品组合
     *
     * @param portfolio
     * @return
     */
    @Override
    @Transactional
    public Long update(Portfolio portfolio) {
        Portfolio portfolioInfo = getById(portfolio.getId());
        portfolioInfo.setOcrNo(portfolio.getOcrNo());
        portfolioInfo.setScanningId(portfolio.getScanningId());
        portfolioInfo.setPrice(portfolio.getPrice());
        portfolioInfo.setStatement(portfolio.getStatement());
        portfolioInfo.setPortfolioStatementId(portfolioInfo.getPortfolioStatementId());
        portfolioInfo.setFree(portfolio.getFree());
        portfolioInfo.setReasonsRejection(portfolio.getReasonsRejection());
        updateById(portfolio);

        PortfolioInfo portfolioInfoDetails = portfolioInfoService.getOne(new LambdaQueryWrapper<PortfolioInfo>()
                .eq(PortfolioInfo::getPortfolioId, portfolio.getId())
                .eq(PortfolioInfo::getStatus, StatusFlag.OK.getCode()));
        if (portfolioInfoDetails != null) {
            portfolioInfoDetails.setOcrNo(portfolio.getOcrNo());
            portfolioInfoDetails.setPrice(portfolio.getPrice());
            portfolioInfoDetails.setStatement(portfolio.getStatement());
            portfolioInfoDetails.setPortfolioStatementId(portfolioInfo.getPortfolioStatementId());
            portfolioInfoDetails.setFree(portfolio.getFree());

            portfolioInfoService.updateById(portfolioInfoDetails);
        }

        return portfolio.getId();
    }

    /**
     * 查询规则使用条数
     *
     * @param scanningId
     * @return
     */
    @Override
    public Long findScanningCount(Long scanningId) {
        return count(new LambdaQueryWrapper<Portfolio>()
                .eq(Portfolio::getScanningId, scanningId));
    }

}
