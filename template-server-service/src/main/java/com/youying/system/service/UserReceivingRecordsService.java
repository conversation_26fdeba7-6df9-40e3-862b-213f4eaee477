package com.youying.system.service;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.UserReceivingRecords;
import com.youying.system.domain.userreceivingrecords.UserGetResponse;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsEx;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsRequest;
import com.youying.system.domain.userreceivingrecords.UserReceivingRecordsResponse;

/**
 * <p>
 * 用户数字藏品领取记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface UserReceivingRecordsService extends IService<UserReceivingRecords> {

    /**
     * 查询用户领取数字藏品
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> listByPage(UserReceivingRecordsRequest request);

    /**
     * 查询藏品领取详情
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsResponse> findCollectionRecipient(UserReceivingRecordsRequest request);

    /**
     * 首页-查询用户领取数字藏品数
     *
     * @param time
     * @param badgeType
     * @return
     */
    Long findUserReceivingRecordsAddCount(TimeRequest time, Integer badgeType);

    /**
     * 导出消费记录
     *
     * @param request
     * @return
     */
    List<UserReceivingRecordsEx> export(UserReceivingRecordsRequest request);

    /**
     * 查询藏品领取条数
     *
     * @param ids 藏品组合ID
     * @return
     */
    Long findPortfolioCountById(Long[] ids);

    /**
     * 查询用户观影剧目、剧场次数
     *
     * @param relationId
     * @param code
     */
    List<UserGetResponse> findUserLookCount(Long relationId, Integer code);

    /**
     * 查询用户是否有领取资格
     *
     * @param userId
     * @param theaterId
     * @param lookNumber
     * @param rankMedalId
     * @param rankMedalInfoId
     * @param repertoireInfoId
     * @param startTime
     * @param endTime
     * @return
     */
    Long findUserLookAssignRepertoire(Long userId,
            Long theaterId,
            Integer lookNumber,
            Long rankMedalId,
            Long rankMedalInfoId,
            Long repertoireInfoId,
            Date startTime,
            Date endTime);

    /**
     * 重新生成藏品组合图片
     * 
     * @param portfolioId
     */
    void regenerateUserPortfolioImage(Long portfolioId);

    /**
     * 重新生成电子票
     * 
     * @param id
     * @return
     */
    void regenerateUserETicketImage(Long id);

}
