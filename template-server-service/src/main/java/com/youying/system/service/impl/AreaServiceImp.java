package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Area;
import com.youying.system.domain.common.area.Tree;
import com.youying.system.mapper.AreaMapper;
import com.youying.system.service.AreaService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 地区表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class AreaServiceImp extends ServiceImpl<AreaMapper, Area> implements AreaService {

    /**
     * 查询地区表树形
     *
     * @return
     */
    @Override
    public List<Tree> findAreaTree() {
        List<Tree> areas = baseMapper.findAreaAll();
        if (CollectionUtils.isNotEmpty(areas)) {
            Map<Long, Tree> treeMap = areas.stream().collect(Collectors.toMap(item -> item.getId(), item -> item));
            List<Tree> areaList = areas.stream().filter(item -> !treeMap.containsKey(item.getParentId())).collect(Collectors.toList());
            for (Tree area : areas) {
                if (treeMap.containsKey(area.getParentId())) {
                    Tree parentTree = treeMap.get(area.getParentId());
                    List<Tree> children = parentTree.getChildren();
                    children.add(area);
                }
            }
            return areaList;
        }
        return areas;
    }

    /**
     * 地区详情
     *
     * @param id
     * @return
     */
    @Override
    public Area details(Long id) {
        return baseMapper.details(id);
    }
}
