package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.RepertoireInfo;
import com.youying.system.domain.repertoireinfo.RepertoireInfoRequest;
import com.youying.system.domain.repertoireinfo.RepertoireInfoResponse;

import java.util.List;

/**
 * <p>
 * 剧目场次信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RepertoireInfoService extends IService<RepertoireInfo> {
    /**
     * 查询剧目场次信息表列表(分页)
     *
     * @param request
     * @return
     */
    List<RepertoireInfoResponse> listByPage(RepertoireInfoRequest request);

    /**
     * 关联修改
     *
     * @param repertoireInfo
     * @return
     */
    Long updateRelevance(RepertoireInfo repertoireInfo);

    /**
     * 查询剧目、剧场关联信息条数
     *
     * @param repertoireId 剧目ID
     * @param theaterId    剧场ID
     * @return
     */
    Long findRepertoireInfoCount(Long repertoireId, Long theaterId);

    /**
     * 查询剧场关联条数(审核通过)
     *
     * @param ids
     * @return
     */
    Long findRepertoireInfoCountByTheaterId(List<Long> ids);

    /**
     * 批量删除剧场关联
     *
     * @param theaterId
     */
    void batchDeleteByTheaterId(List<Long> theaterId);
}
