package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.SouvenirBadgeRequire;

/**
 * <p>
 * 纪念徽章领取规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface SouvenirBadgeRequireService extends IService<SouvenirBadgeRequire> {

    /**
     * 删除纪念徽章领取规则
     *
     * @param souvenirBadgeId
     */
    void deleteBySouvenirBadgeId(Long souvenirBadgeId);

    /**
     * 根据纪念徽章ID查询领取规则
     *
     * @param souvenirBadgeId
     * @return
     */
    SouvenirBadgeRequire findSouvenirBadgeRequire(Long souvenirBadgeId);
}
