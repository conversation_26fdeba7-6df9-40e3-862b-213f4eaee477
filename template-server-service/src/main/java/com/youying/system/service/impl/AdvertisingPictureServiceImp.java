package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.AdvertisingPicture;
import com.youying.system.domain.advertisingpicture.AdvertisingPictureRequest;
import com.youying.system.mapper.AdvertisingPictureMapper;
import com.youying.system.service.AdvertisingPictureService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 广告轮播图表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class AdvertisingPictureServiceImp extends ServiceImpl<AdvertisingPictureMapper, AdvertisingPicture> implements AdvertisingPictureService {

    /**
     * 查询广告轮播图表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<AdvertisingPicture> listByPage(AdvertisingPictureRequest request) {
        return baseMapper.listByPage(request);
    }
}
