package com.youying.system.service;

import com.bestpay.digital.collection.open.base.OpenPageDTO;
import com.bestpay.digital.collection.open.base.OpenResult;
import com.bestpay.digital.collection.open.req.OpenSkuCreateReq;
import com.bestpay.digital.collection.open.req.OpenSkuQueryReq;
import com.bestpay.digital.collection.open.req.OpenUserSendReq;
import com.bestpay.digital.collection.open.resp.OpenAccessUrlResp;
import com.bestpay.digital.collection.open.resp.OpenSendResultResp;
import com.bestpay.digital.collection.open.resp.OpenSkuCreateResp;
import com.bestpay.digital.collection.open.resp.OpenSkuQueryResp;
import com.bestpay.digital.collection.open.resp.OpenUserSendResp;
import com.bestpay.digital.collection.open.resp.QueryUserSkuListResp;
import com.bestpay.digital.collection.open.resp.QueryUserSkuResp;
import com.youying.system.domain.blockchain.GetOpenAccessUrlRequest;
import com.youying.system.domain.blockchain.QuerySendRequest;
import com.youying.system.domain.blockchain.QueryUserSkuListRequest;
import com.youying.system.domain.blockchain.QueryUserSkuRequest;

/**
 * 数字藏品接口
 *
 * <AUTHOR>
 * @Date 2023/4/27
 */
public interface BlockchainService {
    /**
     * 创建藏品
     *
     * @param request
     * @return
     */
    OpenResult<OpenSkuCreateResp> createSku(OpenSkuCreateReq request);

    /**
     * 查看藏品上链状态
     *
     * @param request
     * @return
     */
    OpenResult<OpenSkuQueryResp> querySku(OpenSkuQueryReq request);

    /**
     * 发放数字藏品
     *
     * @param request
     * @return
     */
    OpenResult<OpenUserSendResp> userSend(OpenUserSendReq request);

    /**
     * 查询藏品发放结果
     *
     * @param request
     * @return
     */
    OpenResult<OpenSendResultResp> querySendResult(QuerySendRequest request);

    /**
     * 查看用户账户下的某个藏品的详情信息
     *
     * @param request
     * @return
     */
    OpenResult<QueryUserSkuResp> queryUserSku(QueryUserSkuRequest request);

    /**
     * 查询用户账户下的藏品列表
     *
     * @param request
     * @return
     */
    OpenResult<OpenPageDTO<QueryUserSkuListResp>> queryUserSkuList(QueryUserSkuListRequest request);

    /**
     * 用户生成场景页面的授权访问链接
     *
     * @param request
     * @return
     */
    OpenResult<OpenAccessUrlResp> getOpenAccessUrl(GetOpenAccessUrlRequest request);

    /**
     * 批量上传区块链
     * @param type 类型
     * @param number 数量
     * @param req
     */
//    void batchInsert(Integer type, Integer number , List<OpenSkuCreateReq> req);
}
