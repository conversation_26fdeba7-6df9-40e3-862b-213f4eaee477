package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserTicketGroup;
import com.youying.system.domain.user.UserRequest;
import com.youying.system.domain.userticketgroup.UserTicketGroupResponse;

import java.util.List;

/**
 * <p>
 * 用户电子票分组表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
public interface UserTicketGroupService extends IService<UserTicketGroup> {

    /**
     * 查询用户电子票分组
     *
     * @param request
     * @return
     */
    List<UserTicketGroupResponse> findUserTicketGroup(UserRequest request);
}
