package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Dynamic;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.exception.ServiceException;
import com.youying.system.domain.dynamic.DynamicRequest;
import com.youying.system.domain.dynamic.DynamicResponse;
import com.youying.system.mapper.DynamicMapper;
import com.youying.system.service.DynamicKudosService;
import com.youying.system.service.DynamicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 剧目剧场动态表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class DynamicServiceImp extends ServiceImpl<DynamicMapper, Dynamic> implements DynamicService {
    @Autowired
    private DynamicKudosService dynamicKudosService;

    /**
     * 查询剧目剧场动态表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<DynamicResponse> listByPage(DynamicRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询剧目剧场动态表详情
     *
     * @param id
     * @return
     */
    @Override
    public DynamicResponse details(Long id) {
        return baseMapper.details(id);
    }

    /**
     * 删除动态
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public Integer delete(List<Long> ids) {
        removeBatchByIds(ids);
        dynamicKudosService.deleteByDynamicId(ids);
        return ids.size();
    }

    /**
     * 修改剧目剧场动态状态
     *
     * @param id
     * @return
     */
    @Override
    @Transactional
    public Long updateStatus(Long id) {
        Dynamic dynamic = getById(id);
        if (dynamic == null) {
            throw new ServiceException("数据错误");
        }
        Integer status = StatusFlag.OK.getCode().equals(dynamic.getStatus()) ? StatusFlag.PROHIBITION.getCode() : StatusFlag.OK.getCode();
        dynamic.setStatus(status);
        updateById(dynamic);
        return id;
    }
}
