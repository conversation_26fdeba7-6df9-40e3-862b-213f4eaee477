package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.UserOrder;
import com.youying.system.domain.userorder.UserOrderEx;
import com.youying.system.domain.userorder.UserOrderRequest;
import com.youying.system.domain.userorder.UserOrderResponse;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 用户订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface UserOrderService extends IService<UserOrder> {
    /**
     * 用户订单列表
     *
     * @param request
     * @return
     */
    List<UserOrderResponse> listByPage(UserOrderRequest request);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    UserOrderResponse details(Long id);

    /**
     * 查询商家订单总价
     *
     * @param time
     * @return
     */
    BigDecimal findOrderSumPrice(TimeRequest time);

    /**
     * 导出订单信息
     *
     * @param request
     * @return
     */
    List<UserOrderEx> export(UserOrderRequest request);
}
