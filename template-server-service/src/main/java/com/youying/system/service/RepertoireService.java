package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.system.domain.common.PullRequest;
import com.youying.system.domain.repertoire.RepertoireRequest;
import com.youying.system.domain.repertoire.RepertoireResponse;

import java.util.List;

/**
 * <p>
 * 剧目表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RepertoireService extends IService<Repertoire> {
    /**
     * 查询剧目表列表(分页)
     *
     * @param request
     * @return
     */
    List<RepertoireResponse> listByPage(RepertoireRequest request);

    /**
     * 查询剧目表详情(部分)
     *
     * @param id
     * @return
     */
    RepertoireResponse findRepertoireInfo(Long id);

    /**
     * 删除剧目
     *
     * @param ids
     * @return
     */
    Integer delete(List<Long> ids);

    /**
     * 下拉
     *
     * @param request
     * @return
     */
    List<PullResponse> pull(PullRequest request);

    /**
     * 根据商家ID判断是否添加剧目
     *
     * @param merchantId
     * @return
     */
    Long findRepertoireCountByMerchantId(Long merchantId);

    /**
     * 首页-查询剧目段添加数量
     *
     * @param time
     * @return
     */
    Long findRepertoireAddCount(TimeRequest time);

    /**
     * 修改剧目推荐状态
     *
     * @param id
     * @return
     */
    Long updateRecommend(Long id);
}
