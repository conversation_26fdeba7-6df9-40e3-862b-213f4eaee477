package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.ScanningInfo;
import com.youying.system.mapper.ScanningInfoMapper;
import com.youying.system.service.ScanningInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 纸质票扫描规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Service
public class ScanningInfoServiceImpl extends ServiceImpl<ScanningInfoMapper, ScanningInfo> implements ScanningInfoService {

    /**
     * 根据ID删除规则
     *
     * @param scanningId
     */
    @Override
    @Transactional
    public void deleteByScanningId(Long scanningId) {
        remove(new LambdaQueryWrapper<ScanningInfo>().eq(ScanningInfo::getScanningId, scanningId));
    }

    @Override
    @Transactional
    public void deleteByScanningIds(Long[] scanningIds) {
        remove(new LambdaQueryWrapper<ScanningInfo>().in(ScanningInfo::getScanningId, scanningIds));
    }
}
