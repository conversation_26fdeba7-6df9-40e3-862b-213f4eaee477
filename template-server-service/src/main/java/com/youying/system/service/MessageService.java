package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Message;
import com.youying.system.domain.message.MessageRequest;
import com.youying.system.domain.message.MessageResponse;

import java.util.List;

/**
 * <p>
 * 群发消息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface MessageService extends IService<Message> {

    /**
     * 查询群发消息表列表(分页)
     *
     * @param request
     * @return
     */
    List<MessageResponse> listByPage(MessageRequest request);

    /**
     * 查询群发消息表详情
     *
     * @param id
     * @return
     */
    MessageResponse details(Long id);
}
