package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Notify;
import com.youying.system.domain.notify.NotifyRequest;

import java.util.List;

/**
 * <p>
 * 通知表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface NotifyService extends IService<Notify> {

    /**
     * 发送通知
     *
     * @param notify
     * @return
     */
    Long add(Notify notify);

    /**
     * 修改通知
     *
     * @param notify
     * @return
     */
    Long update(Notify notify);

    /**
     * 推送通知
     */
    void pushNotify();

    /**
     * 通知列表
     *
     * @param request
     * @return
     */
    List<Notify> listByPage(NotifyRequest request);
}
