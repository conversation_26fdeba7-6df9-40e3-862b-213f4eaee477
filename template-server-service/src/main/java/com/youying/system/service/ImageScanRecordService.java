package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.ImageScanRecord;
import com.youying.common.core.page.PageDomain;
import com.youying.system.domain.imagescanrecord.ImageScanRecordResponse;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public interface ImageScanRecordService extends IService<ImageScanRecord> {

    /**
     * 查询纸质票扫描记录
     *
     * @param pageDomain
     * @return
     */
    List<ImageScanRecordResponse> list(PageDomain pageDomain);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ImageScanRecordResponse details(Long id);
}
