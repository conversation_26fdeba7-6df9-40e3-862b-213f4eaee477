package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Comment;
import com.youying.system.domain.comment.CommentRequest;
import com.youying.system.domain.comment.CommentResponse;

import java.util.List;

/**
 * <p>
 * 剧目剧场评论 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface CommentService extends IService<Comment> {

    /**
     * 查询剧目剧场评论列表(分页)
     *
     * @param request
     * @return
     */
    List<CommentResponse> listByPage(CommentRequest request);

    /**
     * @param id
     * @return
     */
    Comment findCommentById(Long id);

    /**
     * 查询剧目剧场评论详情
     *
     * @param id
     * @return
     */
    CommentResponse details(Long id);
}
