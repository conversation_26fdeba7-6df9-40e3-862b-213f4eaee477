package com.youying.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.system.domain.portfolio.PortfolioRequest;
import com.youying.system.domain.portfolio.PortfolioResponse;
import com.youying.system.domain.portfolio.PortfolioWithInfoRequest;
import com.youying.system.domain.portfolio.PortfolioWithInfoResponse;

/**
 * <p>
 * 藏品组合表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface PortfolioService extends IService<Portfolio> {
    /**
     * 藏品组合表列表
     *
     * @param request
     * @return
     */
    List<PortfolioResponse> listByPage(PortfolioRequest request);

    /**
     * 删除藏品组合
     *
     * @param ids
     * @return
     */
    Integer delete(Long[] ids);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    PortfolioResponse details(Long id);

    /**
     * 商品审核
     *
     * @param portfolio
     * @return
     */
    Long audit(Portfolio portfolio);

    /**
     * 修改商品组合
     *
     * @param portfolio
     * @return
     */
    Long update(Portfolio portfolio);

    /**
     * 查询规则使用条数
     *
     * @param scanningId
     * @return
     */
    Long findScanningCount(Long scanningId);

    // ========== 以下为Portfolio与PortfolioInfo关联的增删查改方法 ==========

    /**
     * 新增Portfolio与PortfolioInfo关联记录
     *
     * @param request 请求参数
     * @return 新增记录的Portfolio ID
     */
    Long portfolioInfoCreate(PortfolioWithInfoRequest request);

    /**
     * 根据ID查询Portfolio与PortfolioInfo关联详情
     *
     * @param id Portfolio ID
     * @return 详情信息
     */
    PortfolioWithInfoResponse portfolioInfoDetails(Long id);

    /**
     * 分页查询Portfolio与PortfolioInfo关联列表
     *
     * @param request 查询条件
     * @return 列表数据
     */
    List<PortfolioWithInfoResponse> portfolioInfoListByPage(PortfolioRequest request);

    /**
     * 更新Portfolio与PortfolioInfo关联记录
     *
     * @param request 更新参数
     * @return 更新记录的Portfolio ID
     */
    Long portfolioInfoUpdate(PortfolioWithInfoRequest request);

    /**
     * 删除Portfolio与PortfolioInfo关联记录
     *
     * @param ids Portfolio ID数组
     * @return 删除记录数
     */
    Integer portfolioInfoDelete(Long[] ids);

}
