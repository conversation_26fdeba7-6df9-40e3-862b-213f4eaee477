package com.youying.system.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.system.domain.portfolio.PortfolioRequest;
import com.youying.system.domain.portfolio.PortfolioResponse;

/**
 * <p>
 * 藏品组合表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface PortfolioService extends IService<Portfolio> {
    /**
     * 藏品组合表列表
     *
     * @param request
     * @return
     */
    List<PortfolioResponse> listByPage(PortfolioRequest request);

    /**
     * 删除藏品组合
     *
     * @param ids
     * @return
     */
    Integer delete(Long[] ids);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    PortfolioResponse details(Long id);

    /**
     * 商品审核
     *
     * @param portfolio
     * @return
     */
    Long audit(Portfolio portfolio);

    /**
     * 修改商品组合
     *
     * @param portfolio
     * @return
     */
    Long update(Portfolio portfolio);

    /**
     * 查询规则使用条数
     *
     * @param scanningId
     * @return
     */
    Long findScanningCount(Long scanningId);

}
