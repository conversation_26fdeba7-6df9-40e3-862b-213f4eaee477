package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserLeaderboard;
import com.youying.system.domain.user.UserRequest;
import com.youying.system.domain.userleaderboard.UserLeaderboardResponse;

import java.util.List;

/**
 * <p>
 * 用户排行榜表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
public interface UserLeaderboardService extends IService<UserLeaderboard> {

    /**
     * 查询用户排行榜表
     *
     * @param request
     * @return
     */
    List<UserLeaderboardResponse> findUserLeaderboard(UserRequest request);
}
