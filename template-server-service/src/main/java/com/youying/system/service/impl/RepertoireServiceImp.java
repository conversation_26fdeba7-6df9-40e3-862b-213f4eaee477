package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.common.enums.Enums.RecommendFlag;
import com.youying.common.exception.ServiceException;
import com.youying.system.domain.common.PullRequest;
import com.youying.system.domain.repertoire.RepertoireRequest;
import com.youying.system.domain.repertoire.RepertoireResponse;
import com.youying.system.mapper.RepertoireMapper;
import com.youying.system.service.DigitalAvatarService;
import com.youying.system.service.RankMedalService;
import com.youying.system.service.RepertoireInfoService;
import com.youying.system.service.RepertoireService;
import com.youying.system.service.RepertoireTicketService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 剧目表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class RepertoireServiceImp extends ServiceImpl<RepertoireMapper, Repertoire> implements RepertoireService {
    @Autowired
    private RepertoireInfoService repertoireInfoService;
    @Autowired
    private RepertoireTicketService repertoireTicketService;
    @Autowired
    private DigitalAvatarService digitalAvatarService;
    @Autowired
    private RankMedalService rankMedalService;

    /**
     * 查询剧目表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<RepertoireResponse> listByPage(RepertoireRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询剧目表详情(部分)
     *
     * @param id
     * @return
     */
    @Override
    public RepertoireResponse findRepertoireInfo(Long id) {
        return baseMapper.findRepertoireInfo(id);
    }

    /**
     * 删除剧目
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public Integer delete(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            for (Long repertoireId : ids) {
                // 判断剧目剧场是否关联
                Long repertoireInfoCount = repertoireInfoService.findRepertoireInfoCount(repertoireId, null);
                if (repertoireInfoCount > 0) {
                    throw new ServiceException("剧目与剧场关联，无法删除");
                }
                // 判断剧目是否存在数字藏品 1、电子票 2、数字头像 3、等级勋章
                // 电子票
                Long repertoireTicketCount = repertoireTicketService.findRepertoireTicketCount(repertoireId, null);
                if (repertoireTicketCount > 0) {
                    throw new ServiceException("剧目关联电子票，无法删除");
                }
                // 数字头像
                Long digitalAvatarCount = digitalAvatarService.findDigitalAvatarCount(repertoireId, null);
                if (digitalAvatarCount > 0) {
                    throw new ServiceException("剧目关联数字头像，无法删除");
                }
                // 等级勋章
                Long rankMedalCount = rankMedalService.findRankMedalCount(repertoireId, null);
                if (rankMedalCount > 0) {
                    throw new ServiceException("剧目关联等级勋章，无法删除");
                }
            }
            removeBatchByIds(ids);
        }
        return ids.size();
    }

    /**
     * 下拉
     *
     * @param request
     * @return
     */
    @Override
    public List<PullResponse> pull(PullRequest request) {
        return baseMapper.pull(request);
    }

    /**
     * 根据商家ID判断是否添加剧目
     *
     * @param merchantId
     * @return
     */
    @Override
    public Long findRepertoireCountByMerchantId(Long merchantId) {
        return count(new LambdaQueryWrapper<Repertoire>()
                .eq(Repertoire::getMerchantId, merchantId));
    }

    /**
     * 首页-查询剧目段添加数量
     *
     * @param time
     * @return
     */
    @Override
    public Long findRepertoireAddCount(TimeRequest time) {
        return baseMapper.findRepertoireAddCount(time);
    }

    /**
     * 修改剧目推荐状态
     *
     * @param id
     * @return
     */
    @Override
    public Long updateRecommend(Long id) {
        Repertoire repertoire = getById(id);
        if (RecommendFlag.isDefault(repertoire.getRecommend())) {
            List<Repertoire> repertoireList = list(new LambdaQueryWrapper<Repertoire>()
                    .eq(Repertoire::getRecommend, RecommendFlag.RECOMMEND.getCode())
                    .orderByAsc(Repertoire::getRecommendTime));
            if (CollectionUtils.isNotEmpty(repertoireList) && repertoireList.size() >= 4) {
                repertoireList.get(0).setRecommend(RecommendFlag.DEFAULT.getCode());
                updateById(repertoireList.get(0));
            }
            repertoire.setRecommend(RecommendFlag.RECOMMEND.getCode());
            repertoire.setRecommendTime(new Date());
        } else {
            repertoire.setRecommend(RecommendFlag.DEFAULT.getCode());
        }
        updateById(repertoire);
        return id;
    }
}
