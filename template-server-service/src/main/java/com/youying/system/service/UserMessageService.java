package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserMessage;
import com.youying.system.domain.usermessage.UserMessageRequest;
import com.youying.system.domain.usermessage.UserMessageResponse;

import java.util.List;

/**
 * <p>
 * 用户会话表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface UserMessageService extends IService<UserMessage> {

    /**
     * 用户会话列表
     *
     * @param request
     * @return
     */
    List<UserMessageResponse> listByPage(UserMessageRequest request);

    /**
     * 查询用户会话表详情
     *
     * @param id
     * @return
     */
    UserMessageResponse details(Long id);
}
