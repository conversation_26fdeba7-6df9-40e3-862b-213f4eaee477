package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.domain.entity.Scanning;
import com.youying.system.domain.scanning.AddScanningRequest;
import com.youying.system.domain.scanning.ScanningRequest;
import com.youying.system.domain.scanning.ScanningResponse;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
public interface ScanningService extends IService<Scanning> {
    /**
     * 纸质票扫描
     *
     * @param request
     * @return
     */
    Long add(AddScanningRequest request);

    /**
     * 纸质票扫描
     *
     * @param request
     * @return
     */
    Long update(AddScanningRequest request);

    /**
     * 删除纸质票规则
     *
     * @param ids
     */
    Long delete(Long[] ids);

    /**
     * 查询纸质票扫描规则
     *
     * @param request
     * @return
     */
    List<ScanningResponse> listByPage(ScanningRequest request);

    /**
     * 纸质票扫描规则详情
     *
     * @param id
     * @return
     */
    ScanningResponse details(Long id);

    /**
     * 下拉
     *
     * @return
     */
    List<PullResponse> pull();
}
