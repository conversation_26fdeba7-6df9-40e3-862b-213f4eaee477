package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Leaderboard;
import com.youying.common.core.page.PageDomain;
import com.youying.system.domain.leaderboard.LeaderboardRequest;
import com.youying.system.domain.userleaderboard.LeaderboardResponse;

import java.util.List;

/**
 * <p>
 * 榜单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
public interface LeaderboardService extends IService<Leaderboard> {

    /**
     * 查询系统榜单
     *
     * @param pageDomain
     * @return
     */
    List<Leaderboard> listByPage(PageDomain pageDomain);

    /**
     * 榜单剧目统计
     *
     * @param request
     * @return
     */
    List<LeaderboardResponse> findRepertoireLeaderboard(LeaderboardRequest request);
}
