package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.DigitalAvatarBlockchain;

import java.util.List;

/**
 * <p>
 * 电子头像区块链表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-21
 */
public interface DigitalAvatarBlockchainService extends IService<DigitalAvatarBlockchain> {

    /**
     * 查询数字头像是否添加
     *
     * @param digitalAvatarId
     * @param status
     * @return
     */
    Long findDigitalAvatarBlockchainCount(Long digitalAvatarId, Integer status);

    /**
     * 查询数字合成图片
     *
     * @param digitalAvatarId
     * @param status
     * @return
     */
    List<DigitalAvatarBlockchain> findDigitalAvatarBlock(Long digitalAvatarId, Integer status);
}
