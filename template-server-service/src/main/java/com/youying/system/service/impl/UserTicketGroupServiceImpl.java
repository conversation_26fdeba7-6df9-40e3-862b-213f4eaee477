package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserTicketGroup;
import com.youying.system.domain.user.UserRequest;
import com.youying.system.domain.userticketgroup.UserTicketGroupResponse;
import com.youying.system.mapper.UserTicketGroupMapper;
import com.youying.system.service.UserTicketGroupService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户电子票分组表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Service
public class UserTicketGroupServiceImpl extends ServiceImpl<UserTicketGroupMapper, UserTicketGroup> implements UserTicketGroupService {

    /**
     * 查询用户电子票分组
     *
     * @param request
     * @return
     */
    @Override
    public List<UserTicketGroupResponse> findUserTicketGroup(UserRequest request) {
        return baseMapper.findUserTicketGroup(request);
    }
}
