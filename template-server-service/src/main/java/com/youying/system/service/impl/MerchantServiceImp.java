package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.constant.Constants;
import com.youying.common.constant.LimitConstants;
import com.youying.common.core.common.PullResponse;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.Merchant;
import com.youying.common.core.domain.entity.MerchantUser;
import com.youying.common.enums.Enums.SexFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.SecurityUtils;
import com.youying.common.utils.StringUtils;
import com.youying.system.domain.common.PullRequest;
import com.youying.system.domain.merchant.MerchantRequest;
import com.youying.system.domain.merchant.MerchantResponse;
import com.youying.system.mapper.MerchantMapper;
import com.youying.system.service.DigitalAvatarService;
import com.youying.system.service.MerchantService;
import com.youying.system.service.MerchantUserService;
import com.youying.system.service.RankMedalService;
import com.youying.system.service.RepertoireService;
import com.youying.system.service.RepertoireTicketService;
import com.youying.system.service.SouvenirBadgeService;
import com.youying.system.service.TheaterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 商家表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class MerchantServiceImp extends ServiceImpl<MerchantMapper, Merchant> implements MerchantService {
    @Autowired
    private MerchantUserService merchantUserService;
    @Autowired
    private RepertoireService repertoireService;
    @Autowired
    private TheaterService theaterService;
    @Autowired
    private DigitalAvatarService digitalAvatarService;
    @Autowired
    private RepertoireTicketService repertoireTicketService;
    @Autowired
    private SouvenirBadgeService souvenirBadgeService;
    @Autowired
    private RankMedalService rankMedalService;

    /**
     * 查询商家表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<MerchantResponse> listByPage(MerchantRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询商家是否存在
     *
     * @param id
     * @param account
     * @return
     */
    @Override
    public Boolean findMerchantExist(Long id, String account, Integer merchantCategory) {
        Merchant merchant = getOne(new LambdaQueryWrapper<Merchant>()
                .eq(Merchant::getAccount, account)
                .eq(Merchant::getMerchantCategory, merchantCategory));
        if (merchant == null) {
            return false;
        } else if (id != null) {
            return !merchant.getId().equals(id);
        }
        return true;
    }

    /**
     * 添加商家
     *
     * @param merchant
     * @return
     */
    @Override
    @Transactional
    public Long add(Merchant merchant) {
        String password = StringUtils.isBlank(merchant.getPassword()) ? Constants.ADMIN_DEFAULT_PWD : merchant.getPassword();
        String pwd = SecurityUtils.encryptPassword(password);
        merchant.setPassword(pwd);
        save(merchant);

        // 添加商家用户
        MerchantUser merchantUser = new MerchantUser();
        merchantUser.setMerchantId(merchant.getId());
        merchantUser.setRoleName("超级管理员");
        merchantUser.setMerchantCategory(merchant.getMerchantCategory());
        merchantUser.setType(LimitConstants.ADMIN_FLAG);
        merchantUser.setName(merchant.getMerchantName());
        merchantUser.setAvatar(merchant.getLogo());
        merchantUser.setSex(SexFlag.UNKNOWN.getCode());
        merchantUser.setPhone(merchant.getAccount());
        merchantUser.setPassword(pwd);
        merchantUser.setStatus(StatusFlag.OK.getCode());
        merchantUserService.save(merchantUser);

        merchant.setMerchantUserId(merchantUser.getId());
        updateById(merchant);

        return merchant.getId();
    }

    /**
     * 下拉
     *
     * @param request
     * @return
     */
    @Override
    public List<PullResponse> pull(PullRequest request) {
        return baseMapper.pull(request);
    }

    /**
     * 删除商家
     *
     * @param ids
     * @return
     */
    @Override
    public Integer delete(List<Long> ids) {
        for (Long id : ids) {
            // 判断是否添加剧目或剧场
            Long repertoireCount = repertoireService.findRepertoireCountByMerchantId(id);
            Long theaterCount = theaterService.findTheaterCountByMerchantId(id);
            // 判断是否添加电子票
            repertoireTicketService.findRepertoireTicketCountByMerchantId(id);
            // 判断是否添加电子头像
            Long digitalAvatarCount = digitalAvatarService.findDigitalAvatarCountByMerchantId(id);
            // 判断是否添加纪念徽章
            Long souvenirBadgeCount = souvenirBadgeService.findSouvenirBadgeCountByMerchantId(id);
            // 判断是否添加等级勋章
            Long rankMedalCount = rankMedalService.findRankMedalCountByMerchantId(id);
            Long count = repertoireCount + theaterCount + digitalAvatarCount + rankMedalCount + souvenirBadgeCount;
            if (count > 0) {
                throw new ServiceException("商户已产生数据，无法删除");
            }
        }
        removeBatchByIds(ids);
        merchantUserService.deleteByMerchantId(ids);
        return ids.size();
    }

    /**
     * 首页-查询商家增量
     *
     * @param time
     * @return
     */
    @Override
    public Long findMerchantAddCount(TimeRequest time) {
        return baseMapper.findMerchantAddCount(time);
    }
}
