package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.UserNotify;

import java.util.List;

/**
 * <p>
 * 用户推送信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface UserNotifyService extends IService<UserNotify> {

    /**
     * 批量添加推送信息
     *
     * @param userNotifyList
     */
    void insertBatchNotifyInfo(List<UserNotify> userNotifyList);

    /**
     * 查询推送消息详情
     *
     * @param id
     * @return
     */
    UserNotify details(Long id);
}
