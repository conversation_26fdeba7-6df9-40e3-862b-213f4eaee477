package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Message;
import com.youying.system.domain.message.MessageRequest;
import com.youying.system.domain.message.MessageResponse;
import com.youying.system.mapper.MessageMapper;
import com.youying.system.service.MessageService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 群发消息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class MessageServiceImp extends ServiceImpl<MessageMapper, Message> implements MessageService {

    /**
     * 查询群发消息表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<MessageResponse> listByPage(MessageRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询群发消息表详情
     *
     * @param id
     * @return
     */
    @Override
    public MessageResponse details(Long id) {
        return baseMapper.details(id);
    }
}
