package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Repertoire;
import com.youying.common.core.domain.entity.RepertoireLabel;
import com.youying.system.domain.repertoire.AddRepertoireLabelRequest;
import com.youying.system.domain.repertoirelabel.RepertoireLabelRequest;
import com.youying.system.domain.repertoirelabel.RepertoireLabelResponse;
import com.youying.system.mapper.RepertoireLabelMapper;
import com.youying.system.service.RepertoireLabelService;
import com.youying.system.service.RepertoireService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 剧目标签表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class RepertoireLabelServiceImp extends ServiceImpl<RepertoireLabelMapper, RepertoireLabel> implements RepertoireLabelService {
    @Autowired
    private RepertoireService repertoireService;

    /**
     * 修改剧目标签
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public Long updateLabel(AddRepertoireLabelRequest request) {
        Long repertoireId = request.getRepertoireId();
        Repertoire repertoire = repertoireService.getById(repertoireId);
        remove(new LambdaQueryWrapper<RepertoireLabel>()
                .eq(RepertoireLabel::getRepertoireId, repertoireId));
        if (CollectionUtils.isNotEmpty(request.getRepertoireLabelList())) {
            request.getRepertoireLabelList().stream().forEach(item -> {
                item.setMerchantId(repertoire.getMerchantId());
                item.setRepertoireId(repertoireId);
            });
            saveOrUpdateBatch(request.getRepertoireLabelList());
        }
        return repertoireId;
    }

    /**
     * 查询剧目标签表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<RepertoireLabelResponse> listByPage(RepertoireLabelRequest request) {
        return baseMapper.listByPage(request);
    }
}
