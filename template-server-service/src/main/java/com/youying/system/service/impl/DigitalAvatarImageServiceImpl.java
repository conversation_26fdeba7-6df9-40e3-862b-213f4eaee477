package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.DigitalAvatarImage;
import com.youying.system.mapper.DigitalAvatarImageMapper;
import com.youying.system.service.DigitalAvatarImageService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 数字头像图片表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
@Service
public class DigitalAvatarImageServiceImpl extends ServiceImpl<DigitalAvatarImageMapper, DigitalAvatarImage> implements DigitalAvatarImageService {

    /**
     * 根据数字头像ID删除图片
     *
     * @param digitalAvatarId
     */
    @Override
    @Transactional
    public void deleteByDigitalAvatarId(Long digitalAvatarId) {
        remove(new LambdaQueryWrapper<DigitalAvatarImage>()
                .eq(DigitalAvatarImage::getDigitalAvatarId, digitalAvatarId));
    }

    /**
     * 根据数字头像查询图片
     *
     * @param digitalAvatarId
     * @return
     */
    @Override
    public List<DigitalAvatarImage> findAvatarImageByDigitalAvatar(Long digitalAvatarId) {
        return list(new LambdaQueryWrapper<DigitalAvatarImage>()
                .eq(DigitalAvatarImage::getDigitalAvatarId, digitalAvatarId));
    }
}
