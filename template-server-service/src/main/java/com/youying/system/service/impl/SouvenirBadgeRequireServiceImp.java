package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.SouvenirBadgeRequire;
import com.youying.system.mapper.SouvenirBadgeRequireMapper;
import com.youying.system.service.SouvenirBadgeRequireService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 纪念徽章领取规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class SouvenirBadgeRequireServiceImp extends ServiceImpl<SouvenirBadgeRequireMapper, SouvenirBadgeRequire> implements SouvenirBadgeRequireService {
    /**
     * 删除纪念徽章领取规则
     *
     * @param souvenirBadgeId
     */
    @Override
    @Transactional
    public void deleteBySouvenirBadgeId(Long souvenirBadgeId) {
        remove(new LambdaQueryWrapper<SouvenirBadgeRequire>()
                .eq(SouvenirBadgeRequire::getSouvenirBadgeId, souvenirBadgeId));
    }

    /**
     * 根据纪念徽章ID查询领取规则
     *
     * @param souvenirBadgeId
     * @return
     */
    @Override
    public SouvenirBadgeRequire findSouvenirBadgeRequire(Long souvenirBadgeId) {
        return getOne(new LambdaQueryWrapper<SouvenirBadgeRequire>()
                .eq(SouvenirBadgeRequire::getSouvenirBadgeId, souvenirBadgeId));
    }
}
