package com.youying.system.service.impl;

import com.bestpay.digital.collection.open.base.OpenBaseService;
import com.bestpay.digital.collection.open.base.OpenPageDTO;
import com.bestpay.digital.collection.open.base.OpenResult;
import com.bestpay.digital.collection.open.req.OpenCreateDropReq;
import com.bestpay.digital.collection.open.req.OpenSkuCreateReq;
import com.bestpay.digital.collection.open.req.OpenSkuQueryReq;
import com.bestpay.digital.collection.open.req.OpenUserSendReq;
import com.bestpay.digital.collection.open.resp.OpenAccessUrlResp;
import com.bestpay.digital.collection.open.resp.OpenSendResultResp;
import com.bestpay.digital.collection.open.resp.OpenSkuCreateResp;
import com.bestpay.digital.collection.open.resp.OpenSkuQueryResp;
import com.bestpay.digital.collection.open.resp.OpenUserSendResp;
import com.bestpay.digital.collection.open.resp.QueryUserSkuListResp;
import com.bestpay.digital.collection.open.resp.QueryUserSkuResp;
import com.youying.common.constant.BlockchainConstants;
import com.youying.common.constant.Constants;
import com.youying.common.enums.Enums.BadgeTypeFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.common.utils.uuid.SnowFlakeUtils;
import com.youying.system.domain.blockchain.GetOpenAccessUrlRequest;
import com.youying.system.domain.blockchain.QuerySendRequest;
import com.youying.system.domain.blockchain.QueryUserSkuListRequest;
import com.youying.system.domain.blockchain.QueryUserSkuRequest;
import com.youying.system.service.BlockchainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/4/27
 */
@Service
@Slf4j
public class BlockchainServiceImpl extends OpenBaseService implements BlockchainService {
//    private static final String businessId = "XS5QS0";
//    private static final String businessSecret = "150015E03A0987E20560242D15712322";

    private static final String businessId = "C6K9N6";
    private static final String businessSecret = "A3F35FC3E040375AA6AFA1860F4C1342";

    /**
     * 创建藏品
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public OpenResult<OpenSkuCreateResp> createSku(OpenSkuCreateReq request) {
        return super.post(
                request,
                businessId,
                businessSecret,
                getRequestId(),
                BlockchainConstants.CREATE_SKU_NAME,
                BlockchainConstants.SERVER_URL,
                OpenSkuCreateResp.class);
    }

    /**
     * @param request
     * @return
     */
    @Override
    public OpenResult<OpenSkuQueryResp> querySku(OpenSkuQueryReq request) {
        return super.post(
                request,
                businessId,
                businessSecret,
                getRequestId(),
                BlockchainConstants.OPEN_SKU_QUERY,
                BlockchainConstants.SERVER_URL,
                OpenSkuQueryResp.class);
    }

    /**
     * 发放数字藏品
     *
     * @param request
     * @return
     */
    @Override
    @Transactional
    public OpenResult<OpenUserSendResp> userSend(OpenUserSendReq request) {
        return super.post(
                request,
                businessId,
                businessSecret,
                getRequestId(),
                BlockchainConstants.USER_SEND_NAME,
                BlockchainConstants.SERVER_URL,
                OpenUserSendResp.class);
    }

    /**
     * 查询藏品发放结果
     *
     * @param request
     * @return
     */
    @Override
    public OpenResult<OpenSendResultResp> querySendResult(QuerySendRequest request) {
        return super.post(
                request,
                businessId,
                businessSecret,
                getRequestId(),
                BlockchainConstants.QUERY_SEND_RESULT_NAME,
                BlockchainConstants.SERVER_URL,
                OpenSendResultResp.class);
    }

    /**
     * 查看用户账户下的某个藏品的详情信息
     *
     * @param request
     * @return
     */
    @Override
    public OpenResult<QueryUserSkuResp> queryUserSku(QueryUserSkuRequest request) {
        return super.post(
                request,
                businessId,
                businessSecret,
                getRequestId(),
                BlockchainConstants.QUERY_USER_SKU_NAME,
                BlockchainConstants.SERVER_URL,
                QueryUserSkuResp.class);
    }

    /**
     * 查询用户账户下的藏品列表
     *
     * @param request
     * @return
     */
    @Override
    public OpenResult<OpenPageDTO<QueryUserSkuListResp>> queryUserSkuList(QueryUserSkuListRequest request) {
        return super.post(
                request,
                businessId,
                businessSecret,
                getRequestId(),
                BlockchainConstants.QUERY_USER_SKU_LIST_NAME,
                BlockchainConstants.SERVER_URL,
                OpenPageDTO.class);
    }

    /**
     * 用户生成场景页面的授权访问链接
     *
     * @param request
     * @return
     */
    @Override
    public OpenResult<OpenAccessUrlResp> getOpenAccessUrl(GetOpenAccessUrlRequest request) {
        return super.post(
                request,
                businessId,
                businessSecret,
                getRequestId(),
                BlockchainConstants.GET_OPEN_ACCESS_URL_NAME,
                BlockchainConstants.SERVER_URL,
                OpenAccessUrlResp.class);
    }

    /**
     * 不同请求需要确保唯一性
     *
     * @return
     */
    protected String getRequestId() {
        return SnowFlakeUtils.sendSnowFlakeId().toString();
    }

    /**
     * 批量上链
     */
    public void batchInsert(String productName, String dropName, Integer number, String description, BadgeTypeFlag badgeType, List<String> imageList) {
        String username = SecurityUtils.getUsername();
        OpenSkuCreateReq createReq = new OpenSkuCreateReq();
        for (String imageUrl : imageList) {
            createReq = new OpenSkuCreateReq();
            createReq.setUserName(username);
            createReq.setProductName(productName);
            createReq.setPreviewPic(imageUrl);
            createReq.setTemplateJson(imageUrl);
            createReq.setDescription(description);
            OpenCreateDropReq openCreateDropReq = new OpenCreateDropReq();
            openCreateDropReq.setUserName(username);
            openCreateDropReq.setDropName(dropName);
            createReq.setTemplateType("PHOTO");
            createReq.setIssuerId(Constants.ISSUER_ID);
            createReq.setManufacturerId(Constants.MANUFACTURER_ID);
            createReq.setProductStock(number);
            createReq.setSkuBrightness("1");
            createReq.setOnChain(true);
            createReq.setCreateDrop(true);
            openCreateDropReq.setDropCount(number);
            openCreateDropReq.setBusinessId(Constants.BUSINESS_ID);
            createReq.setOpenCreateDropReq(openCreateDropReq);
            createReq.setBusinessId(Constants.BUSINESS_ID);

            OpenResult<OpenSkuCreateResp> res = createSku(createReq);
            if (!res.isSuccess()) {
                String errorCode = res.getErrorCode();
                String errorMsg = res.getErrorMsg();
                log.error("图片：{} 上链失败 , --> errorCode: {} , errorMsg: {}", imageUrl, errorCode, errorMsg);
            }
        }
        switch (badgeType) {
            // 数字头像
            case DIGITAL_AVATAR:
                break;
            // 电子票
            case ELECTRONIC_TICKET:
                break;
        }

    }
}
