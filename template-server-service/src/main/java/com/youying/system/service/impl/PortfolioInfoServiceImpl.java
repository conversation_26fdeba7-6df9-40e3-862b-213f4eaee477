package com.youying.system.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.PortfolioInfo;
import com.youying.system.mapper.PortfolioInfoMapper;
import com.youying.system.service.PortfolioInfoService;

/**
 * <p>
 * 藏品组合表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-24
 */
@Service
public class PortfolioInfoServiceImpl extends ServiceImpl<PortfolioInfoMapper, PortfolioInfo>
        implements PortfolioInfoService {

    /**
     * 关闭商品老数据
     *
     * @param portfolioId
     */
    @Override
    @Transactional
    public void updateStatus(Long portfolioId) {
        baseMapper.updateStatus(portfolioId);
    }
}
