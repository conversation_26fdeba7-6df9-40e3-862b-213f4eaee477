package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Comment;
import com.youying.system.domain.comment.CommentRequest;
import com.youying.system.domain.comment.CommentResponse;
import com.youying.system.mapper.CommentMapper;
import com.youying.system.service.CommentService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 剧目剧场评论 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class CommentServiceImp extends ServiceImpl<CommentMapper, Comment> implements CommentService {
    /**
     * 查询剧目剧场评论列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<CommentResponse> listByPage(CommentRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询评论详情
     *
     * @param id
     * @return
     */
    @Override
    public Comment findCommentById(Long id) {
        return baseMapper.findCommentById(id);
    }

    /**
     * 查询剧目剧场评论详情
     *
     * @param id
     * @return
     */
    @Override
    public CommentResponse details(Long id) {
        return baseMapper.details(id);
    }
}
