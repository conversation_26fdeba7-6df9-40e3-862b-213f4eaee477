package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.RankMedal;
import com.youying.system.domain.rankmedal.RankMedalRequest;
import com.youying.system.domain.rankmedal.RankMedalResponse;

import java.util.List;

/**
 * <p>
 * 等级勋章表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RankMedalService extends IService<RankMedal> {
    /**
     * 查询等级勋章表列表(分页)
     *
     * @param request
     * @return
     */
    List<RankMedalResponse> listByPage(RankMedalRequest request);

    /**
     * 删除等级勋章
     *
     * @param ids
     * @return
     */
    Integer delete(List<Long> ids);

    /**
     * 查询等级勋章剧目、剧场关联条数
     *
     * @param repertoireId
     * @param theaterId
     * @return
     */
    Long findRankMedalCount(Long repertoireId, Long theaterId);

    /**
     * 查询等级勋章表详情
     *
     * @param id
     * @return
     */
    RankMedalResponse details(Long id);

    /**
     * 根据商家ID判断是否添加等级勋章
     *
     * @param merchantId
     * @return
     */
    Long findRankMedalCountByMerchantId(Long merchantId);

    /**
     * 查询剧场与等级勋章关联条数
     *
     * @param theaterId
     * @return
     */
    Long findRankMedalCountByTheaterId(List<Long> theaterId);

    /**
     * 根据剧场ID批量删除剧等级勋章
     *
     * @param theaterId
     */
    void batchDeleteByTheaterId(List<Long> theaterId);

    /**
     * 推送等级勋章
     *
     * @param rankMedal
     */
    void sendRankMedal(RankMedal rankMedal);
}
