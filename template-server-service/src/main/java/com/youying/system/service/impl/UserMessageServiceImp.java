package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.UserMessage;
import com.youying.system.domain.usermessage.UserMessageRequest;
import com.youying.system.domain.usermessage.UserMessageResponse;
import com.youying.system.mapper.UserMessageMapper;
import com.youying.system.service.UserMessageService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户会话表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class UserMessageServiceImp extends ServiceImpl<UserMessageMapper, UserMessage> implements UserMessageService {

    /**
     * 用户会话列表
     *
     * @param request
     * @return
     */
    @Override
    public List<UserMessageResponse> listByPage(UserMessageRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 查询用户会话表详情
     *
     * @param id
     * @return
     */
    @Override
    public UserMessageResponse details(Long id) {
        return baseMapper.details(id);
    }
}
