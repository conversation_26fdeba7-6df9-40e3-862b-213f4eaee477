package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.TicketGroup;
import com.youying.common.core.page.PageDomain;
import com.youying.system.mapper.TicketGroupMapper;
import com.youying.system.service.TicketGroupService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 电子票分组表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-09
 */
@Service
public class TicketGroupServiceImpl extends ServiceImpl<TicketGroupMapper, TicketGroup> implements TicketGroupService {

    /**
     * 查询电子票分组
     *
     * @param pageDomain
     * @return
     */
    @Override
    public List<TicketGroup> listByPage(PageDomain pageDomain) {
        return baseMapper.listByPage(pageDomain);
    }
}
