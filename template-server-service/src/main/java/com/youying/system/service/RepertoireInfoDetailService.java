package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.RepertoireInfoDetail;
import com.youying.system.domain.repertoireinfodetail.RepertoireInfoDetailResponse;

import java.util.List;

/**
 * <p>
 * 剧目剧场场次表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
public interface RepertoireInfoDetailService extends IService<RepertoireInfoDetail> {

    /**
     * 根据剧目ID查询场次表列表
     *
     * @param repertoireId
     * @return
     */
    List<RepertoireInfoDetailResponse> listByRepertoireId(Long repertoireId);
}
