package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.DigitalAvatar;
import com.youying.common.enums.Enums.AuditFlag;
import com.youying.common.exception.ServiceException;
import com.youying.common.utils.StringUtils;
import com.youying.system.domain.common.AuditRequest;
import com.youying.system.domain.digitalavatar.DigitalAvatarRequest;
import com.youying.system.domain.digitalavatar.DigitalAvatarResponse;
import com.youying.system.mapper.DigitalAvatarMapper;
import com.youying.system.service.DigitalAvatarImageService;
import com.youying.system.service.DigitalAvatarService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 数字头像表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class DigitalAvatarServiceImp extends ServiceImpl<DigitalAvatarMapper, DigitalAvatar> implements DigitalAvatarService {
    @Autowired
    private DigitalAvatarImageService digitalAvatarImageService;

    /**
     * 查询数字头像表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<DigitalAvatarResponse> listByPage(DigitalAvatarRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 删除电子头像
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public Integer delete(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            for (Long id : ids) {
                DigitalAvatar digitalAvatar = getById(id);
                if (digitalAvatar != null && AuditFlag.PASS.getCode().equals(digitalAvatar.getAudit())) {
                    throw new ServiceException("数字头像已审核通过，无法删除");
                }
                digitalAvatarImageService.deleteByDigitalAvatarId(id);
            }
            removeBatchByIds(ids);
        }
        return ids.size();
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public DigitalAvatarResponse details(Long id) {
        DigitalAvatar digitalAvatar = getById(id);
        DigitalAvatarResponse res = new DigitalAvatarResponse();
        BeanUtils.copyProperties(res, digitalAvatar);
        res.setDigitalAvatarImageList(digitalAvatarImageService.findAvatarImageByDigitalAvatar(id));
        return res;
    }

    /**
     * 查询数字头像与剧目，剧场关联条数
     *
     * @param repertoireId
     * @param theaterId
     * @return
     */
    @Override
    public Long findDigitalAvatarCount(Long repertoireId, Long theaterId) {
        if (repertoireId != null) {
            return count(new LambdaQueryWrapper<DigitalAvatar>()
                    .eq(DigitalAvatar::getRepertoireId, repertoireId));
        } else {
            return count(new LambdaQueryWrapper<DigitalAvatar>()
                    .eq(DigitalAvatar::getTheaterId, theaterId));
        }
    }

    /**
     * 根据商家ID判断是否添加电子头像
     *
     * @param merchantId
     * @return
     */
    @Override
    public Long findDigitalAvatarCountByMerchantId(Long merchantId) {
        return count(new LambdaQueryWrapper<DigitalAvatar>()
                .eq(DigitalAvatar::getMerchantId, merchantId));
    }

    /**
     * @param request
     * @return
     */
    @Override
    public Long audit(AuditRequest request) {
        DigitalAvatar digitalAvatar = getById(request.getId());
        if (AuditFlag.PASS.getCode().equals(request.getAudit())) {
            if (AuditFlag.PASS.getCode().equals(request.getAudit()) && AuditFlag.WAIT.getCode().equals(digitalAvatar.getAuditFlag())) {
                digitalAvatar.setAuditFlag(request.getAudit());
            }
            digitalAvatar.setAudit(request.getAudit());
            digitalAvatar.setAuditPassTime(new Date());
            digitalAvatar.setReasonsRejection(null);

            // 生成二维码
            // try {
            //     if (StringUtils.isBlank(digitalAvatar.getQrCode())) {
            //         String uploadUrl = FileUploadUtils.upload(WechatUtil.sendWechatQr("pages/detail/repertoire",digitalAvatar.getId()));
            //         digitalAvatar.setQrCode(uploadUrl);
            //     }
            // } catch (IOException e) {
            //    log.error(e.getMessage(), e);
            // }
            updateById(digitalAvatar);

            return digitalAvatar.getId();
        }
        if (StringUtils.isBlank(request.getReasonsRejection())) {
            throw new ServiceException("驳回原因不能为空");
        }
        digitalAvatar.setAudit(request.getAudit());
        digitalAvatar.setReasonsRejection(request.getReasonsRejection());
        updateById(digitalAvatar);
        return digitalAvatar.getId();
    }
}
