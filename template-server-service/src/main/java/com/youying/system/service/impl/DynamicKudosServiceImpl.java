package com.youying.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.DynamicKudos;
import com.youying.system.mapper.DynamicKudosMapper;
import com.youying.system.service.DynamicKudosService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 剧场动态点赞表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-14
 */
@Service
public class DynamicKudosServiceImpl extends ServiceImpl<DynamicKudosMapper, DynamicKudos> implements DynamicKudosService {

    /**
     * 删除动态点赞
     *
     * @param ids
     */
    @Override
    @Transactional
    public void deleteByDynamicId(List<Long> ids) {
        remove(new LambdaQueryWrapper<DynamicKudos>()
                .in(DynamicKudos::getDynamicId, ids));
    }
}
