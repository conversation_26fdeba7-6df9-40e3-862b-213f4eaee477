package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.AutoReply;
import com.youying.system.domain.autoreply.AutoReplyRequest;
import com.youying.system.domain.autoreply.AutoReplyResponse;
import com.youying.system.mapper.AutoReplyMapper;
import com.youying.system.service.AutoReplyService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 剧目剧场自动回复表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class AutoReplyServiceImp extends ServiceImpl<AutoReplyMapper, AutoReply> implements AutoReplyService {

    /**
     * 查询剧目剧场自动回复表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<AutoReplyResponse> listByPage(AutoReplyRequest request) {
        return baseMapper.listByPage(request);
    }
}
