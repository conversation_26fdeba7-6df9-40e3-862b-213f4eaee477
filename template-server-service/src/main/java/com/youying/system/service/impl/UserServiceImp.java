package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.constant.Constants;
import com.youying.common.core.common.TimeRequest;
import com.youying.common.core.domain.entity.User;
import com.youying.common.core.domain.entity.UserSetting;
import com.youying.common.enums.Enums.DeleteFlag;
import com.youying.common.enums.Enums.StatusFlag;
import com.youying.common.utils.SecurityUtils;
import com.youying.common.utils.SnowFlake;
import com.youying.common.utils.StringUtils;
import com.youying.system.domain.common.CountResponse;
import com.youying.system.domain.user.UserRequest;
import com.youying.system.domain.user.UserResponse;
import com.youying.system.mapper.UserMapper;
import com.youying.system.service.UserService;
import com.youying.system.service.UserSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Service
public class UserServiceImp extends ServiceImpl<UserMapper, User> implements UserService {
    @Autowired
    private UserSettingService userSettingService;

    /**
     * 查询用户表列表(分页)
     *
     * @param request
     * @return
     */
    @Override
    public List<UserResponse> listByPage(UserRequest request) {
        return baseMapper.listByPage(request);
    }

    /**
     * 添加用户
     *
     * @param user
     * @return
     */
    @Override
    @Transactional
    public Long add(User user) {
        user.setNo(SnowFlake.getSnowFlakeId());
        String pwd = StringUtils.isBlank(user.getPassword()) ? Constants.USER_DEFAULT_PWD : user.getPassword();
        user.setPassword(SecurityUtils.encryptPassword(pwd));
        user.setRankMedalInfoId(0L);
        user.setAmount(BigDecimal.ZERO);
        user.setSumLook(0);
        user.setStatus(StatusFlag.OK.getCode());
        user.setDeleted(DeleteFlag.OK.getCode());
        save(user);

        UserSetting userSetting = new UserSetting();
        userSetting.setUserId(user.getId());
        userSetting.setCommentNotify(1);
        userSetting.setWellNotify(1);
        userSetting.setIssueNotify(1);
        userSetting.setGroupMessageNotify(1);
        userSetting.setMessageNotify(1);
        userSettingService.save(userSetting);

        return user.getId();
    }

    /**
     * 删除用户
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public Integer delete(List<Long> ids) {
        baseMapper.deleteUser(ids);
        return ids.size();
    }

    /**
     * 首页-查询用户性别数量
     *
     * @param time
     * @return
     */
    @Override
    public List<CountResponse> findUserSexCount(TimeRequest time) {
        return baseMapper.findUserSexCount(time);
    }
}
