<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserLeaderboardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserLeaderboard">
        <id column="id" property="id"/>
        <result column="leaderboard_id" property="leaderboardId"/>
        <result column="user_receiving_records_id" property="userReceivingRecordsId"/>
        <result column="user_id" property="userId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="type" property="type"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , leaderboard_id, user_receiving_records_id, user_id, repertoire_id, `type`, create_by, create_time, update_by, update_time
    </sql>

    <select id="findUserLeaderboard"
            resultType="com.youying.system.domain.userleaderboard.UserLeaderboardResponse">
        SELECT
            l.`name`,
            r.`name` AS repertoireName,
            ul.create_time
        FROM
            t_user_leaderboard AS ul
            LEFT JOIN t_leaderboard AS l ON l.id = ul.leaderboard_id
            LEFT JOIN t_repertoire AS r ON r.id = ul.repertoire_id
        WHERE
            ul.user_id = #{userId}
        ORDER BY
            l.type,
            l.sort,
            ul.create_time DESC
    </select>

</mapper>
