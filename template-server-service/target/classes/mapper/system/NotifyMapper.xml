<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.NotifyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Notify">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="body" property="body"/>
        <result column="port" property="port"/>
        <result column="push_type" property="pushType"/>
        <result column="timed_release_time" property="timedReleaseTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, body, `port`, push_type, timed_release_time, create_by, create_time, update_by, update_time
    </sql>

    <update id="updateNotifyById">
        UPDATE t_notify
        SET title = #{title},
            body = #{body},
            push_pass_time = #{pushPassTime},
            timed_release_time = #{timedReleaseTime},
            `status` = #{status}
        WHERE
            id = #{id}
    </update>

    <select id="findPushNotify" resultType="com.youying.common.core.domain.entity.Notify">
        SELECT
            *
        FROM
            t_notify AS n
        WHERE
            n.push_type = 3
            AND n.`status` = 0
            AND NOW() >= timed_release_time
    </select>

    <select id="listByPage" resultType="com.youying.common.core.domain.entity.Notify">
        SELECT
            *
        FROM
            t_notify AS n
        <where>
            <if test="keyword != null and keyword != ''">
                AND n.`title` LIKE CONCAT('%',#{keyword},'%')
            </if>
            <if test="port != null and port != ''">
                AND (FIND_IN_SET(n.`port`, #{port}) OR n.port = '3')
            </if>
            <if test="pushType != null and pushType != ''">
                AND FIND_IN_SET(n.push_type, #{pushType})
            </if>
            <if test="status != null and status != ''">
                AND FIND_IN_SET(n.`status`, #{status})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(n.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        ORDER BY FIELD(n.`status`, 0,1,2) , n.create_time DESC
    </select>

</mapper>
