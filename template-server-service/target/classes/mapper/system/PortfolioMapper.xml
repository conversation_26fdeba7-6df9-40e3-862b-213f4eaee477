<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.PortfolioMapper">

    <resultMap id="portfolioMap" type="PortfolioResponse">
        <id column="id" property="id"/>
        <result column="digital_avatar_id" property="digitalAvatarId"/>
        <result column="repertoire_ticket_id" property="repertoireTicketId"/>
        <collection property="digitalAvatarImageList" select="digitalAvatarImageQuery" column="digital_avatar_id=digital_avatar_id" ofType="DigitalAvatarImage" />
        <collection property="repertoireTicket" select="repertoireTicketQuery" column="repertoire_ticket_id=repertoire_ticket_id" javaType="RepertoireTicket" />
    </resultMap>

    <select id="digitalAvatarImageQuery" resultType="DigitalAvatarImage">
        SELECT
            image,
            `group`
        FROM
            t_digital_avatar_image
        WHERE
            digital_avatar_id = #{digital_avatar_id}
        ORDER BY
            sort
    </select>

    <select id="repertoireTicketQuery" resultType="RepertoireTicket">
        SELECT
            cover_front,
            cover_reverse,
            common_image
        FROM
            t_repertoire_ticket
        WHERE
            id = #{repertoire_ticket_id}
    </select>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Portfolio">
        <id column="id" property="id"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="repertoire_ticket_id" property="repertoireTicketId"/>
        <result column="digital_avatar_id" property="digitalAvatarId"/>
        <result column="introduction" property="introduction"/>
        <result column="issued_quantity" property="issuedQuantity"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="price" property="price"/>
        <result column="audit" property="audit"/>
        <result column="audit_flag" property="auditFlag"/>
        <result column="audit_pass_time" property="auditPassTime"/>
        <result column="reasons_rejection" property="reasonsRejection"/>
        <result column="deleted" property="deleted"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="ticket_type" property="ticketType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , theater_id, repertoire_id, repertoire_ticket_id, digital_avatar_id, introduction, issued_quantity, start_time, end_time, price, audit, audit_flag, audit_pass_time, reasons_rejection, deleted, `status`, create_by, create_time, update_by, update_time
    </sql>

    <!-- 插入藏品组合记录 -->
    <insert id="insertPortfolio" parameterType="com.youying.common.core.domain.entity.Portfolio" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_portfolio (
            ocr_no, scanning_id, `no`, `name`, batch, merchant_id, theater_id, repertoire_id,
            repertoire_ticket_id, digital_avatar_id, introduction, issued_quantity, start_time,
            end_time, free, price, portfolio_statement_id, statement, seat_status, look_status,
            sold_out, audit, audit_flag, audit_pass_time, update_cause, reasons_rejection,
            deleted, `status`, create_by, create_time, update_by, update_time
        ) VALUES (
            #{ocrNo}, #{scanningId}, #{no}, #{name}, #{batch}, #{merchantId}, #{theaterId}, #{repertoireId},
            #{repertoireTicketId}, #{digitalAvatarId}, #{introduction}, #{issuedQuantity}, #{startTime},
            #{endTime}, #{free}, #{price}, #{portfolioStatementId}, #{statement}, #{seatStatus}, #{lookStatus},
            #{soldOut}, #{audit}, #{auditFlag}, #{auditPassTime}, #{updateCause}, #{reasonsRejection},
            #{deleted}, #{status}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}
        )
    </insert>

    <select id="listByPage" resultMap="portfolioMap">
        SELECT
            p.id,
            p.batch,
            p.theater_id,
            t.`name` as theaterName,
            p.repertoire_id,
            r.`name` AS repertoireName,
            p.repertoire_ticket_id,
            p.digital_avatar_id,
            p.introduction,
            p.issued_quantity,
            p.start_time,
            p.end_time,
            p.price,
            p.audit,
            p.audit_flag,
            p.audit_pass_time,
            p.reasons_rejection,
            p.`status`,
            p.seat_status,
            p.look_status,
            rt.cover_front,
            rt.cover_reverse,
            rt.common_image AS repertoireTicketCommonImage,
            da.common_image AS digitalAvatarCommonImage,
            (SELECT COUNT(1) FROM t_user_receiving_records WHERE portfolio_id = p.id AND badge_type = 1) AS getNum,
            (SELECT COUNT(1) FROM t_user_receiving_records WHERE portfolio_id = p.id AND badge_type = 1 AND upgrade_status = '1') AS upgradeNum,
            p.sold_out,
            p.statement,
            p.ocr_no,
            p.scanning_id
        FROM
            t_portfolio AS p
            LEFT JOIN t_theater as t on t.id = p.theater_id
            LEFT JOIN t_repertoire as r on r.id = p.repertoire_id
            LEFT JOIN t_repertoire_ticket as rt on rt.id = p.repertoire_ticket_id
            LEFT JOIN t_digital_avatar as da on da.id = p.digital_avatar_id
        <where>
            p.deleted = 1
            <if test="keyword != null and keyword != ''">
                AND
                (
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
            <if test="merchantId != null and merchantId != ''">
                AND FIND_IN_SET(p.merchant_id, #{merchantId})
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(p.theater_id, #{theaterId})
            </if>
            <if test="repertoireId != null and repertoireId != ''">
                AND FIND_IN_SET(p.repertoire_id, #{repertoireId})
            </if>
            <if test="audit != null and audit != ''">
                AND FIND_IN_SET(p.audit, #{audit})
            </if>
            <if test ="ticketType != null and ticketType != ''">
                AND FIND_IN_SET(p.ticket_type, #{ticketType})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(p.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        ORDER BY p.create_time DESC
    </select>


    <select id="details" resultMap="portfolioMap">
        SELECT
            p.id,
            p.name,
            p.batch,
            p.theater_id,
            t.`name` as theaterName,
            p.repertoire_id,
            r.`name` AS repertoireName,
            p.repertoire_ticket_id,
            p.digital_avatar_id,
            p.introduction,
            p.issued_quantity,
            p.start_time,
            p.end_time,
            p.price,
            p.audit,
            p.audit_flag,
            p.audit_pass_time,
            p.reasons_rejection,
            p.`status`,
            p.statement,
            p.portfolio_statement_id,
            p.sold_out,
            da.common_image AS digitalAvatarCommonImage,
            p.ocr_no,
            p.free,
            p.update_cause
        FROM
            t_portfolio AS p
            LEFT JOIN t_theater as t on t.id = p.theater_id
            LEFT JOIN t_repertoire as r on r.id = p.repertoire_id
            LEFT JOIN t_digital_avatar as da on da.id = p.digital_avatar_id
        WHERE
            p.id = #{id}
    </select>

    <!--
    <select id="find" resultType="com.youying.system.domain.portfolio.PortfolioAiResponse">
        SELECT
            p.id,
            p.ocr_no,
            p.sold_out,
            p.scanning_id,
            p.seat_status,
            pi.id AS portfolioId,
            pi.NO,
            pi.`name`,
            pi.theater_id,
            pi.repertoire_id,
            pi.repertoire_ticket_id,
            pi.digital_avatar_id,
            pi.cover_front,
            pi.cover_reverse,
            pi.common_image,
            pi.digital_avatar_common_image,
            t.`name` AS theaterName,
            t.`short_name` AS theaterShortName,
            r.`name` AS repertoireName,
            r.`short_name` AS repertoireShortName
        FROM
            t_portfolio_info AS pi
            LEFT JOIN t_portfolio AS p ON p.id = pi.portfolio_id
            LEFT JOIN t_repertoire AS r ON r.id = p.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = p.theater_id
        WHERE
            r.`name` = #{repertoireName}
            AND t.`name` = #{theaterName}
            AND pi.`status` = 1
            AND p.`status` = 1
            AND p.audit_flag = 2
            AND p.deleted = 1
            AND t.`status` = 1
            AND t.deleted = 1
            AND t.audit = 2
            AND r.`status` = 1
            AND r.deleted = 1
            AND r.audit = 2
    </select>
 -->
</mapper>
