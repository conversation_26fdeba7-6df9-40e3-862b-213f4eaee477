<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.MessageMapper">

    <resultMap id="detailsMap" type="MessageResponse">
        <id column="id" property="id"/>
        <collection property="userList" select="userQuery" column="id" ofType="userResponse" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Message">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="body" property="body"/>
        <result column="sms_notify" property="smsNotify"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, theater_id, repertoire_id, body, sms_notify, create_by, create_time
    </sql>

    <select id="userQuery" resultType="userResponse">
        SELECT
            umi.id,
            u.`name`,
            u.avatar,
            u.phone,
            rmi.rank_medal_name,
            rmi.`name` AS `rankMedalLevel`,
            rmi.color AS rankMedalColor
        FROM
            t_user_message_info AS umi
            LEFT JOIN t_user AS u ON u.id = umi.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
        WHERE
            umi.message_id = #{id}
    </select>

    <select id="listByPage" resultType="com.youying.system.domain.message.MessageResponse">
        SELECT
            m.id,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            m.body,
            m.create_time,
            m.sms_notify
        FROM
            t_message AS m
            LEFT JOIN t_theater AS t ON t.id = m.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = m.repertoire_id
        <where>
            <choose>
                <when test="merchantCategory == '1'.toString()">
                    AND m.repertoire_id > 0
                </when>
                <when test="merchantCategory == '2'.toString()">
                    AND m.theater_id > 0
                </when>
                <otherwise>
                    AND 1 = 0
                </otherwise>
            </choose>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (m.body LIKE CONCAT('%',#{keyword},'%'))
                OR
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
            <if test="repertoireId != null and repertoireId != ''">
                AND FIND_IN_SET(m.repertoire_id, #{repertoireId})
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(m.theater_id, #{theaterId})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(m.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        ORDER BY
            m.create_time DESC,
            m.id
    </select>

    <select id="details" resultMap="detailsMap">
        SELECT
            m.id,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            m.body,
            m.create_time,
            m.sms_notify
        FROM
            t_message AS m
            LEFT JOIN t_theater AS t ON t.id = m.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = m.repertoire_id
        WHERE
            m.id = #{id}
    </select>

</mapper>
