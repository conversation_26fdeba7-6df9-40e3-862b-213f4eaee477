<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserTreasureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserTreasure">
        <result column="user_id" property="userId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, theater_id, repertoire_id, create_time
    </sql>

    <select id="findUserTreasureAddCount"
            resultType="com.youying.system.domain.usertreasure.UserTreasureResponse">
        WITH RECURSIVE date_seq ( time ) AS ( SELECT CAST( #{time.beginTime} AS DATE ) UNION ALL SELECT DATE_ADD( time, INTERVAL 1 DAY ) FROM date_seq WHERE time &lt; #{time.endTime} )
        SELECT
            date_seq.time AS `time`,
            COUNT( ut.theater_id ) AS `theaterCount`,
            COUNT( ut.repertoire_id ) AS `repertoireCount`
        FROM
            date_seq
            LEFT JOIN t_user_treasure AS ut ON DATE( ut.create_time ) = date_seq.time
            AND (ut.create_time BETWEEN #{time.beginTime} AND #{time.endTime})
        GROUP BY
            date_seq.time
    </select>

    <select id="findUserTreasureRepertoireCount" resultType="com.youying.system.domain.common.CountResponse">
        SELECT
            r.`name` AS `type`,
            COUNT( ut.id ) AS `count`
        FROM
            t_repertoire AS r
            LEFT JOIN (
                SELECT
                    id,
                    repertoire_id
                FROM
                    t_user_treasure AS ut
                <where>
                    <if test="time.beginTime != null and time.endTime != null">
                        AND DATE(ut.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
                    </if>
                </where>
            ) AS ut ON ut.repertoire_id = r.id
        WHERE
            r.audit = 2
            AND r.deleted = 1
        GROUP BY
            r.id
    </select>

    <select id="findUserTreasureTheaterCount" resultType="com.youying.system.domain.common.CountResponse">
        SELECT
            t.`name` AS `type`,
            COUNT( ut.id ) AS `count`
        FROM
            t_theater AS t
        LEFT JOIN (
        SELECT
            id,
            theater_id
        FROM
            t_user_treasure AS ut
            <where>
                <if test="time.beginTime != null and time.endTime != null">
                    AND DATE(ut.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
                </if>
            </where>
            ) AS ut ON ut.theater_id = t.id
        WHERE
            t.audit = 2
            AND t.deleted = 1
        GROUP BY
            t.id
    </select>

    <select id="findRepertoireFans" resultType="com.youying.system.domain.usertreasure.UserTreasureResponse">
        SELECT
            u.`name`,
            u.phone,
            rmi.rank_medal_name,
            rmi.`name` AS `rankMedalLevel`,
            rmi.color AS rankMedalColor,
            ut.create_time,
            ( SELECT COUNT( 1 ) FROM t_user_receiving_records WHERE user_id = ut.user_id AND ut.repertoire_id = ut.repertoire_id AND badge_type = 1 LIMIT 1) AS `lookCount`
        FROM
            t_user_treasure AS ut
            LEFT JOIN t_user AS u ON u.id = ut.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
        <where>
            ut.repertoire_id = #{repertoireId}
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(ut.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="lookStatus != null">
                <choose>
                    <when test="lookStatus == '0'.toString()">
                        AND ( SELECT COUNT( 1 ) FROM t_user_receiving_records WHERE user_id = ut.user_id AND ut.repertoire_id = ut.repertoire_id AND badge_type = 1 LIMIT 1) = 0
                    </when>
                    <when test="lookStatus == '1'.toString()">
                        AND ( SELECT COUNT( 1 ) FROM t_user_receiving_records WHERE user_id = ut.user_id AND ut.repertoire_id = ut.repertoire_id AND badge_type = 1 LIMIT 1) > 0
                    </when>
                    <otherwise>
                        AND 0 = 1
                    </otherwise>
                </choose>
            </if>
        </where>
            ORDER BY ut.create_time DESC
    </select>

    <select id="findTheaterFans" resultType="com.youying.system.domain.usertreasure.UserTreasureResponse">
        SELECT
            u.`name`,
            u.phone,
            rmi.rank_medal_name,
            rmi.`name` AS `rankMedalLevel`,
            rmi.color AS rankMedalColor,
            ut.create_time,
            ( SELECT COUNT( 1 ) FROM t_user_receiving_records WHERE user_id = ut.user_id AND ut.theater_id = ut.theater_id AND badge_type = 1 LIMIT 1) AS `lookCount`
        FROM
            t_user_treasure AS ut
            LEFT JOIN t_user AS u ON u.id = ut.user_id
            LEFT JOIN t_rank_medal_info AS rmi ON rmi.id = u.rank_medal_info_id
        <where>
            ut.theater_id = #{theaterId}
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(ut.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="lookStatus != null">
                <choose>
                    <when test="lookStatus == '0'.toString()">
                        AND ( SELECT COUNT( 1 ) FROM t_user_receiving_records WHERE user_id = ut.user_id AND ut.theater_id = ut.theater_id AND badge_type = 1 LIMIT 1) = 0
                    </when>
                    <when test="lookStatus == '1'.toString()">
                        AND ( SELECT COUNT( 1 ) FROM t_user_receiving_records WHERE user_id = ut.user_id AND ut.theater_id = ut.theater_id AND badge_type = 1 LIMIT 1) > 0
                    </when>
                    <otherwise>
                        AND 0 = 1
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY ut.create_time DESC
    </select>

    <select id="exportRepertoireFans" resultType="com.youying.system.domain.usertreasure.UserTreasureEx">
        SELECT
            u.`name`,
            u.phone,
            ut.create_time,
            ( SELECT ( CASE COUNT( 1 ) WHEN 0 THEN '否' ELSE '是' END ) FROM t_user_receiving_records WHERE user_id = ut.user_id AND ut.repertoire_id = ut.repertoire_id AND badge_type = 1 LIMIT 1) AS `lookCount`
        FROM
            t_user_treasure AS ut
            LEFT JOIN t_user AS u ON u.id = ut.user_id
        <where>
            ut.repertoire_id = #{repertoireId}
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(ut.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="lookStatus != null">
                <choose>
                    <when test="lookStatus == '0'.toString()">
                        AND ( SELECT COUNT( 1 ) FROM t_user_receiving_records WHERE user_id = ut.user_id AND ut.repertoire_id = ut.repertoire_id AND badge_type = 1 LIMIT 1) = 0
                    </when>
                    <when test="lookStatus == '1'.toString()">
                        AND ( SELECT COUNT( 1 ) FROM t_user_receiving_records WHERE user_id = ut.user_id AND ut.repertoire_id = ut.repertoire_id AND badge_type = 1 LIMIT 1) > 0
                    </when>
                    <otherwise>
                        AND 0 = 1
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY ut.create_time DESC
    </select>

    <select id="exportTheaterFans" resultType="com.youying.system.domain.usertreasure.UserTreasureEx">
        SELECT
            u.`name`,
            u.phone,
            ut.create_time,
            ( SELECT ( CASE COUNT( 1 ) WHEN 0 THEN '否' ELSE '是' END ) FROM t_user_receiving_records WHERE user_id = ut.user_id AND ut.theater_id = ut.theater_id AND badge_type = 1 LIMIT 1) AS `lookCount`
        FROM
            t_user_treasure AS ut
            LEFT JOIN t_user AS u ON u.id = ut.user_id
        <where>
            ut.theater_id = #{theaterId}
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(ut.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="lookStatus != null">
                <choose>
                    <when test="lookStatus == '0'.toString()">
                        AND ( SELECT COUNT( 1 ) FROM t_user_receiving_records WHERE user_id = ut.user_id AND ut.theater_id = ut.theater_id AND badge_type = 1 LIMIT 1) = 0
                    </when>
                    <when test="lookStatus == '1'.toString()">
                        AND ( SELECT COUNT( 1 ) FROM t_user_receiving_records WHERE user_id = ut.user_id AND ut.theater_id = ut.theater_id AND badge_type = 1 LIMIT 1) > 0
                    </when>
                    <otherwise>
                        AND 0 = 1
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY ut.create_time DESC
    </select>

</mapper>
