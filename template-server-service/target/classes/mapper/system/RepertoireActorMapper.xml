<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.RepertoireActorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.RepertoireActor">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="actor_type" property="actorType"/>
        <result column="picture" property="picture"/>
        <result column="role_name" property="roleName"/>
        <result column="actor_name" property="actorName"/>
        <result column="group_performance_name" property="groupPerformanceName"/>
        <result column="introduction" property="introduction"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, repertoire_id, actor_type, picture, role_name, actor_name, group_performance_name, introduction
    </sql>

</mapper>
