<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.RepertoireInfoDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.RepertoireInfoDetail">
        <id column="id" property="id"/>
        <result column="repertoire_info_id" property="repertoireInfoId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, repertoire_info_id, theater_id, repertoire_id, start_time, end_time
    </sql>

    <select id="listByRepertoireId"
            resultType="com.youying.system.domain.repertoireinfodetail.RepertoireInfoDetailResponse">
        SELECT
            rid.id,
            t.`name` AS theaterName,
            rid.start_time,
            rid.end_time
        FROM
            t_repertoire_info_detail AS rid
            LEFT JOIN t_repertoire_info AS ri ON ri.id = rid.repertoire_info_id
            LEFT JOIN t_theater AS t ON t.id = rid.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = rid.repertoire_id
        WHERE
            rid.repertoire_id = #{repertoireId}
            AND ri.`status` = 1
    </select>

</mapper>
