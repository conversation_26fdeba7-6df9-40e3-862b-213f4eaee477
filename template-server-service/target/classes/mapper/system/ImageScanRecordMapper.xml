<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.ImageScanRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.ImageScanRecord">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="device_model" property="deviceModel"/>
        <result column="portfolio_id" property="portfolioId"/>
        <result column="file_url" property="fileUrl"/>
        <result column="ip_address" property="ipAddress"/>
        <result column="body" property="body"/>
        <result column="word_list" property="wordList"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_id, device_model, portfolio_id, file_url, ip_address, body, word_list, create_time
    </sql>

    <select id="list" resultType="imageScanRecordResponse">
        SELECT
            isr.id,
            isr.user_id,
            isr.device_model,
            isr.portfolio_id,
            isr.file_url,
            isr.ip_address,
            isr.create_time,
            u.`name`,
            u.`phone`,
            (SELECT COUNT(1) FROM t_user_receiving_records WHERE file_url = isr.file_url) AS getCount
        FROM
            t_image_scan_record AS isr
            LEFT JOIN t_user AS u ON isr.user_id = u.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND u.`name` LIKE CONCAT('%',#{keyword},'%')
            </if>
        </where>
        ORDER BY
            isr.create_time DESC
    </select>

    <select id="details" resultType="com.youying.system.domain.imagescanrecord.ImageScanRecordResponse">
        SELECT
            isr.id,
            isr.user_id,
            isr.device_model,
            isr.portfolio_id,
            isr.file_url,
            isr.ip_address,
            isr.body,
            isr.word_list,
            isr.create_time,
            u.`name`,
            u.`phone`,
            (SELECT COUNT(1) FROM t_user_receiving_records WHERE file_url = isr.file_url) AS getCount
        FROM
            t_image_scan_record AS isr
            LEFT JOIN t_user AS u ON isr.user_id = u.id
        WHERE
            isr.id = #{id}
    </select>

    <select id="find" resultType="com.youying.common.core.domain.entity.UserReceivingRecords">
        SELECT
            isr.file_url,
            isr.user_id,
            p.theater_id,
            p.repertoire_id,
            '' AS repertoire_info_detail_id,
            '' AS repertoire_info_id,
            0 AS relation_id,
            1 AS badge_type,
            p.start_time,
            p.end_time,
            '' AS image,
            '' AS seat_number,
            '' AS amount,
            '' AS price,
            '' AS `time`,
            '' AS portfolio_no,
            1416 AS portfolio_id,
            1581 AS portfolio_info_id,
            '0' AS upgrade_status,
            u.`name` AS create_by,
            u.`name` AS update_by
        FROM
            t_image_scan_record AS isr
            LEFT JOIN t_portfolio AS p ON p.id = 1416
            LEFT JOIN t_user AS u ON u.id = isr.user_id
        WHERE
            isr.id in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
