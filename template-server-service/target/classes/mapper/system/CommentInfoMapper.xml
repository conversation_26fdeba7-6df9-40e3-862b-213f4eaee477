<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.CommentInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.CommentInfo">
        <result column="user_id" property="userId"/>
        <result column="comment_id" property="commentId"/>
        <result column="type" property="type"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, comment_id, `type`
    </sql>

</mapper>
