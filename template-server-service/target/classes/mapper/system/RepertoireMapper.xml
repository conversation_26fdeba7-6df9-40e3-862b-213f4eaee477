<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.RepertoireMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.Repertoire">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="repertoire_type_id" property="repertoireTypeId"/>
        <result column="cover_picture" property="coverPicture"/>
        <result column="pictures" property="pictures"/>
        <result column="introduction" property="introduction"/>
        <result column="rating" property="rating"/>
        <result column="recommend" property="recommend"/>
        <result column="good_rating_rate" property="goodRatingRate"/>
        <result column="focus_number" property="focusNumber"/>
        <result column="audit" property="audit"/>
        <result column="audit_pass_time" property="auditPassTime"/>
        <result column="reasons_rejection" property="reasonsRejection"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="deleted" property="deleted"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `name`, repertoire_type_id, cover_picture, pictures, label, introduction, rating, recommend,
        good_rating_rate, focus_number, audit, audit_pass_time, reasons_rejection, merchant_id, deleted, `status`,
        create_by, create_time, update_by, update_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.repertoire.RepertoireResponse">
        SELECT
            r.id,
            r.`name`,
            r.cover_picture,
            r.rating,
            r.good_rating_rate,
            r.`status`,
            r.recommend,
            r.audit,
            r.introduction,
            r.reasons_rejection,
            r.create_time,
            r.focus_number,
            m.merchant_name,
            r.short_name,
            r.qr_code,
            GROUP_CONCAT( DISTINCT rl.`name`) AS repertoireLabel,
            ( SELECT t_rank_medal.`name` FROM t_rank_medal WHERE t_rank_medal.theater_id = r.id LIMIT 1 ) AS rankMedalName,
            ( SELECT COUNT( 1 ) FROM t_comment WHERE t_comment.repertoire_id = r.id AND t_comment.parent_id = 0 ) AS commentCount,
            ( SELECT COUNT( 1 ) FROM t_comment WHERE t_comment.repertoire_id = r.id AND t_comment.parent_id > 0 ) AS interactionCount
        FROM
            t_repertoire AS r
            LEFT JOIN t_merchant AS m ON m.id = r.merchant_id
            LEFT JOIN t_repertoire_label AS rl ON r.id = rl.repertoire_id
            LEFT JOIN t_repertoire_info AS ri ON r.id = ri.repertoire_id
            LEFT JOIN t_theater AS t ON ri.theater_id = t.id
        <where>
            r.deleted = '1'
            <if test="keyword != null and keyword != ''">
                AND r.`name` LIKE CONCAT('%',#{keyword},'%')
            </if>
            <if test="status != null">
                AND r.status = #{status}
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND r.id IN (SELECT rin.repertoire_id FROM t_repertoire_info as rin WHERE  FIND_IN_SET(rin.theater_id, #{theaterId}) AND rin.`status` = 1 )
            </if>
            <if test="merchantId != null and merchantId != ''">
                AND FIND_IN_SET(r.merchant_id, #{merchantId})
            </if>
            <if test="repertoireLabel != null and repertoireLabel != ''">
                AND FIND_IN_SET(rl.id, #{repertoireLabel})
            </if>
            <if test="audit != null and audit != ''">
                AND FIND_IN_SET(r.audit, #{audit})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(r.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="showTime.beginTime != null and showTime.endTime != null">
                AND ( SELECT COUNT(1) FROM t_repertoire_info_detail WHERE t_repertoire_info_detail.repertoire_id = r.id AND DATE(t_repertoire_info_detail.start_time) BETWEEN #{showTime.beginTime} AND #{showTime.endTime} ) > 0
            </if>
        </where>
        GROUP BY
            r.id
        ORDER BY r.recommend DESC, r.create_time DESC, r.id
    </select>

    <select id="findRepertoireInfo" resultType="com.youying.system.domain.repertoire.RepertoireResponse">
        SELECT
            r.id,
            r.`name`,
            r.cover_picture,
            r.introduction,
            r.pictures,
            r.create_time,
            r.rating,
            r.good_rating_rate,
            GROUP_CONCAT( DISTINCT rl.`name` ) AS repertoireLabel,
            m.merchant_name,
            m.merchant_category
        FROM
            t_repertoire AS r
            LEFT JOIN t_repertoire_label AS rl ON r.id = rl.repertoire_id
            LEFT JOIN t_merchant AS m ON m.id = r.merchant_id
        WHERE
            r.id = #{id}
        GROUP BY
            r.id
    </select>

    <select id="pull" resultType="com.youying.common.core.common.PullResponse">
        SELECT
            r.id,
            r.`name`,
            r.merchant_id
        FROM
            t_repertoire AS r
        <where>
            r.deleted = 1
            <if test="merchantId != null">
                AND r.merchant_id = #{merchantId}
            </if>
            <if test="audit != null">
                AND r.audit = #{audit}
            </if>
            <if test="status != null">
                AND r.`status` = #{status}
            </if>
        </where>
        ORDER BY r.create_time DESC , r.id
    </select>

    <select id="findRepertoireAddCount" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM
            t_repertoire AS r
        <where>
            r.deleted = 1
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(r.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
    </select>

</mapper>
