<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.User">
        <id column="id" property="id"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="avatar" property="avatar"/>
        <result column="sex" property="sex"/>
        <result column="phone" property="phone"/>
        <result column="personalized_signature" property="personalizedSignature"/>
        <result column="password" property="password"/>
        <result column="rank_ medal_id" property="rankMedalInfoId"/>
        <result column="amount" property="amount"/>
        <result column="sum_look" property="sumLook"/>
        <result column="status" property="status"/>
        <result column="deleted" property="deleted"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `no`, `name`, avatar, sex, phone, personalized_signature, `password`, rank_ medal_id, amount, sum_look,
        `status`, deleted, create_by, create_time, update_by, update_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.user.UserResponse">
        SELECT
            u.id,
            u.`no`,
            u.`name`,
            u.avatar,
            u.sex,
            u.phone,
            u.personalized_signature,
            u.rank_medal_info_id,
            u.amount,
            u.sum_look,
            u.`status`,
            u.create_time
        FROM
            t_user AS u
        <where>
            u.deleted = 1
            <if test="keyword != null and keyword != ''">
            AND
            (
            (u.`name` LIKE CONCAT('%',#{keyword},'%'))
            OR
            (u.`phone` LIKE CONCAT('%',#{keyword},'%'))
            )
            </if>
            <if test="status != null and status != ''">
                AND FIND_IN_SET(u.status, #{status})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(u.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        ORDER BY u.create_time DESC
    </select>

    <select id="findUserSexCount" resultType="com.youying.system.domain.common.CountResponse">
        SELECT
            u.sex AS `type`,
            COUNT( 1 )  AS `count`
        FROM
            t_user AS u
        <where>
            u.deleted = 1
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(u.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
        </where>
        GROUP BY
            u.sex
    </select>

    <delete id="deleteUser">
        DELETE
            u,
            c,
            c1,
            ci,
            k,
            ul,
            um,
            umi,
            urr,
            us,
            ut
        FROM
            t_user AS u
            LEFT JOIN t_comment AS c ON c.user_id = u.id
            LEFT JOIN t_comment AS c1 ON c1.reply_id = u.id
            LEFT JOIN t_comment_info AS ci ON ci.user_id = u.id
            LEFT JOIN t_kudos AS k ON k.user_id = u.id
            LEFT JOIN t_user_look AS ul ON ul.user_id = u.id
            LEFT JOIN t_user_message AS um ON um.user_id = u.id
            LEFT JOIN t_user_message_info AS umi ON umi.user_id = u.id
            LEFT JOIN t_user_receiving_records AS urr ON urr.user_id = u.id
            LEFT JOIN t_user_setting AS us ON us.user_id = u.id
            LEFT JOIN t_user_treasure AS ut ON ut.user_id = u.id
        <where>
            <foreach item="item" index="index" collection="ids"
                     open="u.id in (" separator="," close=")" nullable="true">
                #{item}
            </foreach>
        </where>
    </delete>
</mapper>
