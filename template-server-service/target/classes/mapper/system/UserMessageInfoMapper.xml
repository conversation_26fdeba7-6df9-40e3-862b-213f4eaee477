<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.UserMessageInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.UserMessageInfo">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="user_message_id" property="userMessageId"/>
        <result column="user_id" property="userId"/>
        <result column="message_id" property="messageId"/>
        <result column="body" property="body"/>
        <result column="look_flag" property="lookFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_id, theater_id, repertoire_id, user_message_id, user_id, message_id, body, look_flag, create_by,
        create_time
    </sql>

</mapper>
