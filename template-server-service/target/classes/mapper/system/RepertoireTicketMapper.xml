<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.RepertoireTicketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.RepertoireTicket">
        <id column="id" property="id"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="cover_front" property="coverFront"/>
        <result column="cover_reverse" property="coverReverse"/>
        <result column="issuer_name" property="issuerName"/>
        <result column="issued_quantity" property="issuedQuantity"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="audit" property="audit"/>
        <result column="reasons_rejection" property="reasonsRejection"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `code`, merchant_id, theater_id, repertoire_id, cover_front, cover_reverse, issuer_name, issued_quantity,
        start_time, end_time, max_price, consume_number, audit, reasons_rejection, create_by, create_time, update_by,
        update_time
    </sql>

    <select id="listByPage" resultType="com.youying.system.domain.repertoireticket.RepertoireTicketResponse">
        SELECT
            rt.id,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            rt.batch,
            rt.cover_front,
            rt.cover_reverse,
            rt.issuer_name,
            rt.issued_quantity,
            rt.start_time,
            rt.end_time,
            rt.audit,
            rt.`status`,
            rt.reasons_rejection,
            m.merchant_name,
            ( SELECT COUNT( 1 ) FROM t_user_receiving_records WHERE badge_type = '1' AND relation_id = rt.id ) AS getCount
        FROM
            t_repertoire_ticket AS rt
            LEFT JOIN t_theater AS t ON t.id = rt.theater_id
            LEFT JOIN t_repertoire AS r ON r.id = rt.repertoire_id
            LEFT JOIN t_merchant AS m ON m.id = rt.merchant_id
        <where>
            <if test="merchantId != null and merchantId != ''">
                AND FIND_IN_SET(rt.merchant_id, #{merchantId})
            </if>
            <if test="repertoireId != null and repertoireId != ''">
                AND FIND_IN_SET(rt.repertoire_id, #{repertoireId})
            </if>
            <if test="theaterId != null and theaterId != ''">
                AND FIND_IN_SET(rt.theater_id, #{theaterId})
            </if>
            <if test="audit != null and audit != ''">
                AND FIND_IN_SET(rt.audit, #{audit})
            </if>
            <if test="time.beginTime != null and time.endTime != null">
                AND DATE(rt.create_time) BETWEEN #{time.beginTime} AND #{time.endTime}
            </if>
            <if test="keyword != null and keyword != ''">
                AND
                (
                (t.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (r.`name` LIKE CONCAT('%',#{keyword},'%'))
                OR
                (m.merchant_name LIKE CONCAT('%',#{keyword},'%'))
                )
            </if>
        </where>
        ORDER BY
            rt.create_time DESC
    </select>

</mapper>
