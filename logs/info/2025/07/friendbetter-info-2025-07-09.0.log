2025-07-09 16:50:52.568 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 1.8.0_281 on macbookofalan.local with PID 96051 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/classes started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin)
2025-07-09 16:50:52.579 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-07-09 16:50:58.829 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 16:50:58.832 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-07-09 16:50:58.921 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-09 16:51:02.306 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-07-09 16:51:02.309 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:51:02.591 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-07-09 16:51:02.596 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:51:03.846 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-07-09 16:51:03.848 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:51:03.886 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-07-09 16:51:03.887 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:51:03.917 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-07-09 16:51:03.917 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:51:03.940 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-07-09 16:51:03.941 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:51:03.959 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-07-09 16:51:03.960 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:51:03.989 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-07-09 16:51:03.990 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:51:04.016 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-07-09 16:51:04.019 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:51:04.050 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-07-09 16:51:04.051 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:51:04.100 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-07-09 16:51:04.101 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:51:04.161 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-07-09 16:51:04.161 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:51:04.551 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-07-09 16:51:04.553 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:51:13.116 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-09 16:51:20.274 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 28.589 seconds (JVM running for 29.628)
2025-07-09 16:51:20.297 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-07-09 16:51:20.297 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-07-09 16:51:20.297 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-07-09 16:51:20.298 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-07-09 16:51:20.298 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-07-09 16:55:59.449 [http-nio-8110-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 16:56:00.764 [schedule-pool-1] INFO  sys-user - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-07-09 16:59:01.136 [Thread-5] INFO  sys-user - ====关闭后台任务任务线程池====
2025-07-09 16:59:01.151 [Thread-5] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-09 16:59:01.179 [Thread-5] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-09 16:59:02.159 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 1.8.0_281 on macbookofalan.local with PID 96051 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/classes started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin)
2025-07-09 16:59:02.161 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-07-09 16:59:04.208 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 16:59:04.212 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-07-09 16:59:04.227 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-09 16:59:05.232 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-07-09 16:59:05.232 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:59:05.340 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-07-09 16:59:05.341 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:59:05.809 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-07-09 16:59:05.809 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:59:05.833 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-07-09 16:59:05.833 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:59:05.848 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-07-09 16:59:05.848 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:59:05.862 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-07-09 16:59:05.864 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:59:05.882 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-07-09 16:59:05.882 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:59:05.911 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-07-09 16:59:05.912 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:59:05.940 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-07-09 16:59:05.940 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:59:05.987 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-07-09 16:59:05.988 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:59:06.014 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-07-09 16:59:06.014 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:59:06.030 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-07-09 16:59:06.031 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:59:06.265 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-07-09 16:59:06.266 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 16:59:11.486 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
2025-07-09 16:59:16.273 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 14.533 seconds (JVM running for 505.636)
2025-07-09 16:59:16.285 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-07-09 16:59:16.285 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-07-09 16:59:16.285 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-07-09 16:59:16.286 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-07-09 16:59:16.286 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-07-09 16:59:31.136 [http-nio-8110-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 17:06:13.310 [http-nio-8110-exec-23] ERROR c.y.f.w.e.GlobalExceptionHandler - 请求地址'/scanning/listByPage',发生系统异常.
org.apache.catalina.connector.ClientAbortException: java.io.IOException: Broken pipe
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:310)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:273)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:523)
	at java.io.FilterOutputStream.flush(FilterOutputStream.java:140)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1193)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1008)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:456)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:290)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.youying.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.youying.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: Broken pipe
	at sun.nio.ch.FileDispatcherImpl.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:47)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:469)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1384)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:773)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:726)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:716)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:573)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:221)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1255)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:402)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:306)
	... 110 common frames omitted
2025-07-09 17:06:13.327 [http-nio-8110-exec-23] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.youying.framework.web.exception.GlobalExceptionHandler#handleException(Exception, HttpServletRequest)
org.apache.catalina.connector.ClientAbortException: java.io.IOException: Broken pipe
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:310)
	at org.apache.catalina.connector.OutputBuffer.flush(OutputBuffer.java:273)
	at org.apache.catalina.connector.CoyoteOutputStream.flush(CoyoteOutputStream.java:118)
	at org.springframework.security.web.util.OnCommittedResponseWrapper$SaveContextServletOutputStream.flush(OnCommittedResponseWrapper.java:523)
	at java.io.FilterOutputStream.flush(FilterOutputStream.java:140)
	at com.fasterxml.jackson.core.json.UTF8JsonGenerator.flush(UTF8JsonGenerator.java:1193)
	at com.fasterxml.jackson.databind.ObjectWriter.writeValue(ObjectWriter.java:1008)
	at org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter.writeInternal(AbstractJackson2HttpMessageConverter.java:456)
	at org.springframework.http.converter.AbstractGenericHttpMessageConverter.write(AbstractGenericHttpMessageConverter.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:290)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:428)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:142)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1327)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1138)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.youying.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:37)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:121)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:115)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:105)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.youying.framework.security.filter.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:40)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.io.IOException: Broken pipe
	at sun.nio.ch.FileDispatcherImpl.write0(Native Method)
	at sun.nio.ch.SocketDispatcher.write(SocketDispatcher.java:47)
	at sun.nio.ch.IOUtil.writeFromNativeBuffer(IOUtil.java:93)
	at sun.nio.ch.IOUtil.write(IOUtil.java:65)
	at sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:469)
	at org.apache.tomcat.util.net.NioChannel.write(NioChannel.java:135)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.doWrite(NioEndpoint.java:1384)
	at org.apache.tomcat.util.net.SocketWrapperBase.doWrite(SocketWrapperBase.java:773)
	at org.apache.tomcat.util.net.SocketWrapperBase.flushBlocking(SocketWrapperBase.java:726)
	at org.apache.tomcat.util.net.SocketWrapperBase.flush(SocketWrapperBase.java:716)
	at org.apache.coyote.http11.Http11OutputBuffer$SocketOutputBuffer.flush(Http11OutputBuffer.java:573)
	at org.apache.coyote.http11.filters.ChunkedOutputFilter.flush(ChunkedOutputFilter.java:157)
	at org.apache.coyote.http11.Http11OutputBuffer.flush(Http11OutputBuffer.java:221)
	at org.apache.coyote.http11.Http11Processor.flush(Http11Processor.java:1255)
	at org.apache.coyote.AbstractProcessor.action(AbstractProcessor.java:402)
	at org.apache.coyote.Response.action(Response.java:209)
	at org.apache.catalina.connector.OutputBuffer.doFlush(OutputBuffer.java:306)
	... 113 common frames omitted
2025-07-09 17:10:14.504 [Thread-8] INFO  sys-user - ====关闭后台任务任务线程池====
2025-07-09 17:10:14.516 [Thread-8] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-07-09 17:10:14.524 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-07-09 17:10:14.544 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-07-09 17:10:15.459 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 1.8.0_281 on macbookofalan.local with PID 96051 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/classes started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin)
2025-07-09 17:10:15.466 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-07-09 17:10:16.975 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 17:10:16.982 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-07-09 17:10:16.992 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-09 17:10:17.798 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-07-09 17:10:17.798 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 17:10:17.875 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-07-09 17:10:17.875 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 17:10:18.230 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-07-09 17:10:18.230 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 17:10:18.241 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-07-09 17:10:18.241 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 17:10:18.254 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-07-09 17:10:18.255 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 17:10:18.265 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-07-09 17:10:18.265 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 17:10:18.280 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-07-09 17:10:18.280 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 17:10:18.299 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-07-09 17:10:18.299 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 17:10:18.315 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-07-09 17:10:18.316 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 17:10:18.329 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-07-09 17:10:18.329 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 17:10:18.343 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-07-09 17:10:18.343 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 17:10:18.353 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-07-09 17:10:18.353 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 17:10:18.762 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-07-09 17:10:18.762 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-09 17:10:25.282 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} inited
2025-07-09 17:10:28.713 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 13.559 seconds (JVM running for 1178.087)
2025-07-09 17:10:28.723 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-07-09 17:10:28.723 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-07-09 17:10:28.723 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-07-09 17:10:28.723 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-07-09 17:10:28.723 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-07-09 17:12:20.188 [http-nio-8110-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 17:12:23.075 [http-nio-8110-exec-4] ERROR c.a.druid.filter.stat.StatFilter - slow sql 1710 millis. SELECT
            r.id,
            r.`name`,
            r.merchant_id
        FROM
            t_repertoire AS r
         WHERE r.deleted = 1
            
            
                AND r.audit = ? 
        ORDER BY r.create_time DESC , r.id[2]
