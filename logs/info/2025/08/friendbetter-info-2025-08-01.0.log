2025-08-01 17:02:05.303 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 1.8.0_281 on macbookofalan.local with PID 84174 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/classes started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin)
2025-08-01 17:02:05.313 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-01 17:02:12.384 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 17:02:12.389 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-01 17:02:12.491 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 17:02:15.912 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-08-01 17:02:15.913 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:02:16.196 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-08-01 17:02:16.197 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:02:17.598 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-08-01 17:02:17.599 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:02:17.651 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-08-01 17:02:17.651 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:02:17.681 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-08-01 17:02:17.682 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:02:17.714 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-08-01 17:02:17.716 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:02:17.739 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-08-01 17:02:17.741 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:02:17.769 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-08-01 17:02:17.771 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:02:17.802 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-08-01 17:02:17.802 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:02:17.823 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-08-01 17:02:17.823 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:02:17.856 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-08-01 17:02:17.857 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:02:17.887 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-08-01 17:02:17.887 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:02:18.258 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-08-01 17:02:18.258 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:02:26.719 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-01 17:02:34.809 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 30.349 seconds (JVM running for 31.522)
2025-08-01 17:02:34.849 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-08-01 17:02:34.850 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-08-01 17:02:34.850 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-08-01 17:02:34.852 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-08-01 17:02:34.852 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-08-01 17:05:24.990 [http-nio-8110-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 17:05:26.235 [schedule-pool-1] INFO  sys-user - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-08-01 17:11:57.120 [Thread-5] INFO  sys-user - ====关闭后台任务任务线程池====
2025-08-01 17:11:57.149 [Thread-5] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-01 17:11:57.177 [Thread-5] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-01 17:11:58.366 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 1.8.0_281 on macbookofalan.local with PID 84174 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/classes started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin)
2025-08-01 17:11:58.377 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-01 17:12:00.415 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 17:12:00.421 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-01 17:12:00.437 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 17:12:01.649 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-08-01 17:12:01.649 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:01.848 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-08-01 17:12:01.849 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:02.819 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-08-01 17:12:02.820 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:02.845 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-08-01 17:12:02.845 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:02.868 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-08-01 17:12:02.868 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:02.891 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-08-01 17:12:02.891 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:02.921 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-08-01 17:12:02.921 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:02.958 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-08-01 17:12:02.959 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:02.996 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-08-01 17:12:02.996 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:03.026 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-08-01 17:12:03.026 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:03.087 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-08-01 17:12:03.088 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:03.110 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-08-01 17:12:03.111 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:03.672 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-08-01 17:12:03.672 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:10.041 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
2025-08-01 17:12:14.899 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 17.113 seconds (JVM running for 611.594)
2025-08-01 17:12:14.925 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-08-01 17:12:14.927 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-08-01 17:12:14.927 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-08-01 17:12:14.927 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-08-01 17:12:14.927 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-08-01 17:12:17.305 [Thread-8] INFO  sys-user - ====关闭后台任务任务线程池====
2025-08-01 17:12:17.316 [Thread-8] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-08-01 17:12:17.322 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-08-01 17:12:17.340 [Thread-8] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-08-01 17:12:18.299 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 1.8.0_281 on macbookofalan.local with PID 84174 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/classes started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin)
2025-08-01 17:12:18.306 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-01 17:12:19.935 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 17:12:19.940 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-01 17:12:19.950 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 17:12:20.694 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-08-01 17:12:20.695 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:20.768 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-08-01 17:12:20.768 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:21.269 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-08-01 17:12:21.271 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:21.293 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-08-01 17:12:21.294 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:21.316 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-08-01 17:12:21.317 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:21.335 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-08-01 17:12:21.335 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:21.357 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-08-01 17:12:21.357 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:21.387 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-08-01 17:12:21.387 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:21.405 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-08-01 17:12:21.405 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:21.421 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-08-01 17:12:21.422 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:21.436 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-08-01 17:12:21.436 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:21.450 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-08-01 17:12:21.450 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:21.708 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-08-01 17:12:21.708 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:12:28.727 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} inited
2025-08-01 17:12:33.044 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 15.083 seconds (JVM running for 629.74)
2025-08-01 17:12:33.055 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-08-01 17:12:33.055 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-08-01 17:12:33.055 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-08-01 17:12:33.055 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-08-01 17:12:33.055 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-08-01 17:19:42.644 [Thread-12] INFO  sys-user - ====关闭后台任务任务线程池====
2025-08-01 17:19:42.668 [Thread-12] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-08-01 17:19:42.679 [Thread-12] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-08-01 17:19:42.735 [Thread-12] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
2025-08-01 17:19:44.540 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 1.8.0_281 on macbookofalan.local with PID 84174 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/classes started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin)
2025-08-01 17:19:44.546 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-01 17:19:45.919 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 17:19:45.923 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-01 17:19:45.929 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 17:19:46.585 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-08-01 17:19:46.585 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:19:46.656 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-08-01 17:19:46.657 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:19:47.115 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-08-01 17:19:47.116 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:19:47.140 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-08-01 17:19:47.140 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:19:47.159 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-08-01 17:19:47.159 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:19:47.171 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-08-01 17:19:47.171 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:19:47.189 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-08-01 17:19:47.189 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:19:47.204 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-08-01 17:19:47.204 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:19:47.230 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-08-01 17:19:47.231 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:19:47.258 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-08-01 17:19:47.258 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:19:47.278 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-08-01 17:19:47.279 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:19:47.308 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-08-01 17:19:47.309 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:19:47.681 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-08-01 17:19:47.682 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:19:53.965 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} inited
2025-08-01 17:19:58.853 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 14.771 seconds (JVM running for 1075.549)
2025-08-01 17:19:58.859 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-08-01 17:19:58.859 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-08-01 17:19:58.859 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-08-01 17:19:58.860 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-08-01 17:19:58.860 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-08-01 17:21:04.621 [Thread-16] INFO  sys-user - ====关闭后台任务任务线程池====
2025-08-01 17:21:04.631 [Thread-16] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-08-01 17:21:04.636 [Thread-16] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} closing ...
2025-08-01 17:21:04.659 [Thread-16] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} closed
2025-08-01 17:21:05.734 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 1.8.0_281 on macbookofalan.local with PID 84174 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/classes started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin)
2025-08-01 17:21:05.740 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-01 17:21:07.186 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 17:21:07.190 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-01 17:21:07.199 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 17:21:07.952 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-08-01 17:21:07.952 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:08.015 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-08-01 17:21:08.016 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:08.441 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-08-01 17:21:08.441 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:08.458 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-08-01 17:21:08.458 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:08.481 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-08-01 17:21:08.481 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:08.497 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-08-01 17:21:08.497 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:08.516 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-08-01 17:21:08.516 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:08.536 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-08-01 17:21:08.536 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:08.559 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-08-01 17:21:08.559 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:08.579 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-08-01 17:21:08.579 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:08.604 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-08-01 17:21:08.605 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:08.626 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-08-01 17:21:08.626 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:09.015 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-08-01 17:21:09.015 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:13.850 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-5} inited
2025-08-01 17:21:18.055 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 12.607 seconds (JVM running for 1154.751)
2025-08-01 17:21:18.063 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-08-01 17:21:18.063 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-08-01 17:21:18.063 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-08-01 17:21:18.063 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-08-01 17:21:18.063 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-08-01 17:21:20.496 [Thread-20] INFO  sys-user - ====关闭后台任务任务线程池====
2025-08-01 17:21:20.504 [Thread-20] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-08-01 17:21:20.509 [Thread-20] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-5} closing ...
2025-08-01 17:21:20.530 [Thread-20] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-5} closed
2025-08-01 17:21:21.722 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 1.8.0_281 on macbookofalan.local with PID 84174 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/classes started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin)
2025-08-01 17:21:21.728 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-01 17:21:23.212 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 17:21:23.216 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-01 17:21:23.225 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 17:21:24.123 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-08-01 17:21:24.124 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:24.185 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-08-01 17:21:24.186 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:24.586 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-08-01 17:21:24.586 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:24.600 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-08-01 17:21:24.601 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:24.654 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-08-01 17:21:24.654 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:24.668 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-08-01 17:21:24.669 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:24.694 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-08-01 17:21:24.695 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:24.711 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-08-01 17:21:24.711 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:24.729 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-08-01 17:21:24.730 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:24.748 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-08-01 17:21:24.748 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:24.772 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-08-01 17:21:24.772 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:24.791 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-08-01 17:21:24.792 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:25.154 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-08-01 17:21:25.154 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:29.906 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-6} inited
2025-08-01 17:21:34.798 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 13.381 seconds (JVM running for 1171.494)
2025-08-01 17:21:34.806 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-08-01 17:21:34.806 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-08-01 17:21:34.806 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-08-01 17:21:34.807 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-08-01 17:21:34.807 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-08-01 17:21:37.281 [Thread-24] INFO  sys-user - ====关闭后台任务任务线程池====
2025-08-01 17:21:37.287 [Thread-24] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-08-01 17:21:37.293 [Thread-24] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-6} closing ...
2025-08-01 17:21:37.312 [Thread-24] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-6} closed
2025-08-01 17:21:38.375 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 1.8.0_281 on macbookofalan.local with PID 84174 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/classes started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin)
2025-08-01 17:21:38.381 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-01 17:21:39.521 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 17:21:39.522 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-01 17:21:39.526 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 17:21:40.101 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-08-01 17:21:40.102 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:40.165 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-08-01 17:21:40.165 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:40.546 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-08-01 17:21:40.547 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:40.567 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-08-01 17:21:40.567 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:40.577 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-08-01 17:21:40.577 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:40.586 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-08-01 17:21:40.587 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:40.597 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-08-01 17:21:40.597 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:40.611 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-08-01 17:21:40.612 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:40.625 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-08-01 17:21:40.625 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:40.636 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-08-01 17:21:40.636 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:40.646 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-08-01 17:21:40.647 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:40.658 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-08-01 17:21:40.659 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:40.852 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-08-01 17:21:40.853 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:21:47.952 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-7} inited
2025-08-01 17:21:53.375 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 15.276 seconds (JVM running for 1190.071)
2025-08-01 17:21:53.384 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-08-01 17:21:53.385 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-08-01 17:21:53.385 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-08-01 17:21:53.385 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-08-01 17:21:53.385 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-08-01 17:30:46.355 [http-nio-8110-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 17:33:48.138 [Thread-28] INFO  sys-user - ====关闭后台任务任务线程池====
2025-08-01 17:33:48.144 [Thread-28] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-08-01 17:33:48.148 [Thread-28] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-7} closing ...
2025-08-01 17:33:48.171 [Thread-28] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-7} closed
2025-08-01 17:33:49.710 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 1.8.0_281 on macbookofalan.local with PID 84174 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/classes started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin)
2025-08-01 17:33:49.717 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-01 17:33:51.336 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 17:33:51.338 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-01 17:33:51.351 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 17:33:52.420 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-08-01 17:33:52.420 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:33:52.543 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-08-01 17:33:52.543 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:33:53.179 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-08-01 17:33:53.180 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:33:53.197 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-08-01 17:33:53.197 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:33:53.211 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-08-01 17:33:53.212 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:33:53.227 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-08-01 17:33:53.227 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:33:53.244 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-08-01 17:33:53.244 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:33:53.263 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-08-01 17:33:53.263 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:33:53.281 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-08-01 17:33:53.282 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:33:53.299 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-08-01 17:33:53.299 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:33:53.323 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-08-01 17:33:53.324 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:33:53.340 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-08-01 17:33:53.340 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:33:53.610 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-08-01 17:33:53.610 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:33:59.677 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-8} inited
2025-08-01 17:34:03.727 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 14.315 seconds (JVM running for 1920.499)
2025-08-01 17:34:03.737 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-08-01 17:34:03.738 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-08-01 17:34:03.738 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-08-01 17:34:03.738 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-08-01 17:34:03.738 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-08-01 17:34:06.231 [Thread-32] INFO  sys-user - ====关闭后台任务任务线程池====
2025-08-01 17:34:06.237 [Thread-32] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-08-01 17:34:06.241 [Thread-32] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-8} closing ...
2025-08-01 17:34:06.254 [Thread-32] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-8} closed
2025-08-01 17:34:07.332 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 1.8.0_281 on macbookofalan.local with PID 84174 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/classes started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin)
2025-08-01 17:34:07.338 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-01 17:34:08.430 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 17:34:08.430 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-01 17:34:08.434 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 17:34:08.977 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.CommentInfo".
2025-08-01 17:34:08.977 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.CommentInfo ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:34:09.029 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.DynamicKudos".
2025-08-01 17:34:09.029 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.DynamicKudos ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:34:09.392 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysConfig".
2025-08-01 17:34:09.392 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysConfig ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:34:09.408 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictData".
2025-08-01 17:34:09.409 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:34:09.427 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysDictType".
2025-08-01 17:34:09.427 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysDictType ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:34:09.438 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysLogininfor".
2025-08-01 17:34:09.438 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysLogininfor ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:34:09.452 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysMenu".
2025-08-01 17:34:09.453 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:34:09.465 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysOperLog".
2025-08-01 17:34:09.465 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysOperLog ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:34:09.483 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysRole".
2025-08-01 17:34:09.484 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:34:09.495 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysRoleMenu".
2025-08-01 17:34:09.496 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysRoleMenu ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:34:09.514 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.SysUser".
2025-08-01 17:34:09.514 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.SysUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:34:09.527 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.system.domain.SysUserRole".
2025-08-01 17:34:09.528 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.system.domain.SysUserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:34:09.728 [restartedMain] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.youying.common.core.domain.entity.UserTreasure".
2025-08-01 17:34:09.728 [restartedMain] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.youying.common.core.domain.entity.UserTreasure ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-01 17:34:14.633 [restartedMain] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-9} inited
2025-08-01 17:34:18.789 [restartedMain] INFO  com.youying.ApplicationMain - Started ApplicationMain in 11.721 seconds (JVM running for 1935.56)
2025-08-01 17:34:18.795 [restartedMain] INFO  com.youying.common.utils.CommonUtils - DataSource: null
2025-08-01 17:34:18.795 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host IP: 127.0.0.1
2025-08-01 17:34:18.795 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Host Name: macbookofalan.local
2025-08-01 17:34:18.796 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Server Port: 8110
2025-08-01 17:34:18.796 [restartedMain] INFO  com.youying.common.utils.CommonUtils - Swagger: http://localhost:8110/swagger-ui/index.html

2025-08-01 17:34:21.296 [Thread-36] INFO  sys-user - ====关闭后台任务任务线程池====
2025-08-01 17:34:21.302 [Thread-36] WARN  o.s.c.a.CommonAnnotationBeanPostProcessor - Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-08-01 17:34:21.307 [Thread-36] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-9} closing ...
2025-08-01 17:34:21.322 [Thread-36] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-9} closed
2025-08-01 17:34:22.557 [restartedMain] INFO  com.youying.ApplicationMain - Starting ApplicationMain using Java 1.8.0_281 on macbookofalan.local with PID 84174 (/Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin/template-api-service/target/classes started by alan in /Users/<USER>/Documents/git/actcity-20250521/backend/digital-collections-admin)
2025-08-01 17:34:22.563 [restartedMain] INFO  com.youying.ApplicationMain - The following 1 profile is active: "dev"
2025-08-01 17:34:23.612 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 17:34:23.613 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-08-01 17:34:23.617 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
