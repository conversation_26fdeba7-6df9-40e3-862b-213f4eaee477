package com.youying.framework.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.youying.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/4/9
 */
@Slf4j
@Component
public class MybatisPlusConfig implements MetaObjectHandler {
    /**
     * 新增自动注入
     *
     * @param metaObject
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        try {
            this.strictInsertFill(metaObject, "createBy", String.class, SecurityUtils.getUsername());
            this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
        } catch (Exception e) {

        }
        updateFill(metaObject);
    }

    /**
     * 修改自动注入
     *
     * @param metaObject
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            this.setFieldValByName("updateBy", SecurityUtils.getUsername(), metaObject);
            this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
        } catch (Exception e) {

        }
    }
}
