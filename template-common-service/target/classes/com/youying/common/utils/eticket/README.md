# 电子票合成工具

本工具根据《用户电子票合成规范》实现，提供Java版本的电子票生成功能。

## 功能特性

- ✅ 完全按照规范实现的rpx单位转换（2倍像素密度）
- ✅ **智能画布尺寸计算**：根据背景图片宽高比等比例计算画布高度
- ✅ 支持背景图片加载和圆角处理
- ✅ 支持圆形头像的生成和绘制
- ✅ 精确的布局定位和字体渲染
- ✅ 价格数据的标准化处理
- ✅ 长短座位信息的自适应显示
- ✅ 完善的异常处理和降级方案
- ✅ 支持文件输出和字节数组返回

## 核心类说明

### 1. TicketInfo - 票据信息模型
包含电子票生成所需的所有票据信息：
- `ticketH`: 票据高度（rpx单位）
- `coverFrontUrl`: 背景图片URL
- `repertoireCoverPictureUrl`: 剧场头像URL
- `repertoire`: 剧目名称
- `serialNumber`: 专属编号
- `dateTime`: 演出时间
- `theater`: 演出地点
- `price`: 票价（支持多种格式）
- `seat`: 座位信息

### 2. UserInfo - 用户信息模型
包含用户相关信息：
- `avatarUrl`: 用户头像URL
- `name`: 用户昵称

### 3. ETicketCompositeUtil - 合成工具类
提供静态方法进行电子票生成：
- `generateETicket()`: 生成电子票并保存到文件
- `generateETicketBytes()`: 生成电子票并返回字节数组

## 使用方式

### 基本使用示例

```java
// 创建票据信息
TicketInfo ticketInfo = new TicketInfo();
ticketInfo.setTicketH(463);
ticketInfo.setCoverFrontUrl("https://example.com/background.jpg");
ticketInfo.setRepertoireCoverPictureUrl("https://example.com/avatar.jpg");
ticketInfo.setRepertoire("音乐剧《狮子王》");
ticketInfo.setSerialNumber("A123456789");
ticketInfo.setDateTime("2025年1月28日 19:30");
ticketInfo.setTheater("上海大剧院");
ticketInfo.setPrice("￥380元");
ticketInfo.setSeat("1层A区5排8座");

// 创建用户信息
UserInfo userInfo = new UserInfo();
userInfo.setAvatarUrl("https://example.com/user_avatar.jpg");
userInfo.setName("张三");

// 生成电子票
boolean success = ETicketCompositeUtil.generateETicket(ticketInfo, userInfo, "/path/to/output.png");
```

### 生成字节数组（适用于接口返回）

```java
byte[] ticketBytes = ETicketCompositeUtil.generateETicketBytes(ticketInfo, userInfo);
if (ticketBytes != null) {
    // 可以直接返回给前端或保存到数据库
    return ResponseEntity.ok()
        .contentType(MediaType.IMAGE_PNG)
        .body(ticketBytes);
}
```

## 技术实现细节

### 1. 单位转换
- 严格按照规范：1rpx = 0.5px（iPhone6标准）
- 使用2倍像素密度处理，确保高清显示
- 所有尺寸都经过精确的rpx到像素转换

### 2. 智能画布尺寸计算
- **核心算法**：先获取背景图片的宽度和高度，然后根据画布的实际宽度等比例计算出画布的实际高度
- **等比例缩放**：`画布高度 = 画布宽度 × (背景图片高度 / 背景图片宽度)`
- **降级处理**：当无法获取背景图片时，使用 `ticketH` 作为备用高度
- **日志记录**：详细记录背景图片尺寸和计算得出的画布高度，便于调试

### 3. 图片处理
- 支持从URL加载图片，包含连接超时和读取超时设置
- 自动生成圆形头像，使用抗锯齿处理
- 背景图片支持缩放覆盖整个画布
- 图片加载失败时使用渐变背景作为降级方案

### 4. 文本渲染
- 支持中文字符的正确渲染
- 精确的字体大小、颜色和位置控制
- **文字垂直居中优化**：正确计算文字在背景区域内的垂直居中位置
- 长座位信息的自动换行处理
- 支持rgba和十六进制颜色格式

### 5. 价格处理
根据规范实现价格数据的标准化：
```java
// 移除"元"、"￥"、"¥"字符
// 进行数值转换和格式化
// 零值特殊处理
String processedPrice = processPrice("￥380元"); // 返回: "380"
```

### 6. 布局精确定位
- 所有元素使用绝对定位，确保像素级精确
- 严格按照规范的尺寸和间距
- 支持圆角矩形和复杂布局
- **圆角处理优化**：正确使用RoundRectangle2D.Float的arcWidth和arcHeight参数（直径而非半径）
- **底部内容区布局优化**：严格按照规范重新计算所有元素的相对位置，确保正确的间距和对齐
- **演出信息定位优化**：正确实现left: 18rpx, 宽度: 484rpx的定位要求，在指定宽度内居中显示

### 7. 异常处理
- 图片加载失败时的降级处理
- 数据缺失时的默认值处理
- 网络异常的超时机制
- 完善的日志记录

## 配置参数

工具类中预定义了所有规范参数，无需额外配置：

```java
// 画布配置
private static final int CANVAS_WIDTH_RPX = 520;
private static final int BORDER_RADIUS_RPX = 40;
private static final double PIXEL_RATIO = 2.0;

// 区域高度
private static final int TOP_CONTENT_HEIGHT_RPX = 150;
private static final int BOTTOM_CONTENT_HEIGHT_RPX = 313;

// 颜色定义
private static final Color COLOR_PURPLE = new Color(57, 37, 132);
private static final Color COLOR_LIGHT_PURPLE = new Color(230, 218, 255);
```

## 性能优化

1. **图片缓存**: 建议在生产环境中实现图片缓存机制
2. **异步处理**: 对于批量生成，建议使用异步处理
3. **内存管理**: 工具会自动释放Graphics2D资源
4. **网络优化**: 设置了合理的连接和读取超时时间

## 注意事项

1. **图片URL**: 确保图片URL可访问，支持HTTP/HTTPS
2. **字体支持**: 确保系统支持"微软雅黑"字体，或修改为系统可用字体
3. **文件权限**: 生成文件时确保目标目录有写入权限
4. **内存使用**: 大批量生成时注意内存使用情况
5. **网络环境**: 图片加载依赖网络环境，建议配置网络代理或使用本地图片

## 集成示例

### Spring Boot Controller集成

```java
@RestController
@RequestMapping("/api/eticket")
public class ETicketController {
    
    @PostMapping("/generate")
    public ResponseEntity<byte[]> generateETicket(@RequestBody ETicketRequest request) {
        TicketInfo ticketInfo = convertToTicketInfo(request);
        UserInfo userInfo = convertToUserInfo(request);
        
        byte[] ticketBytes = ETicketCompositeUtil.generateETicketBytes(ticketInfo, userInfo);
        
        if (ticketBytes != null) {
            return ResponseEntity.ok()
                .contentType(MediaType.IMAGE_PNG)
                .header("Content-Disposition", "attachment; filename=eticket.png")
                .body(ticketBytes);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
```

## 版本信息

- **当前版本**: 1.0.0
- **Java版本要求**: JDK 8+
- **依赖**: SLF4J（日志记录）
- **规范版本**: 用户电子票合成规范 v1.0

## 许可证

本工具遵循项目整体许可证协议。 