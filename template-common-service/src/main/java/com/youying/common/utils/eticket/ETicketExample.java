package com.youying.common.utils.eticket;

import java.io.FileOutputStream;
import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 电子票合成工具使用示例
 * 演示如何使用ETicketCompositeUtil生成电子票
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class ETicketExample {

    private static final Logger logger = LoggerFactory.getLogger(ETicketExample.class);

    /**
     * 综合测试方法 - 测试所有修复效果
     */
    public static void comprehensiveTest() {
        logger.info("=== 综合测试所有修复效果 ===");

        // 创建票据信息
        TicketInfo ticketInfo = new TicketInfo();
        ticketInfo.setCoverFrontUrl(
                "https://sys.jiaozuoer.cn/api/profile/upload/2025/07/19/%E3%80%90%E4%B8%8A%E6%B5%B7%E3%80%91%E6%B2%AA%E5%89%A7%E3%83%BB%E7%B2%BE%E9%80%89%E6%8A%98%E5%AD%90%E6%88%8F_20250719104553A893.png");
        ticketInfo.setRepertoireCoverPictureUrl(
                "https://sys.jiaozuoer.cn/api/profile/upload/2023/09/11/%E5%AD%A4%E6%B3%A8%E4%B8%80%E6%8E%B73_20230911133724A026.jpg");
        ticketInfo.setRepertoire("「北京」中央音乐学院音乐厅音乐剧《狮子王》");
        ticketInfo.setSerialNumber("A123456789"); // 测试编号背景圆角和文字垂直居中
        ticketInfo.setDateTime("2025年1月28日 19:30");
        ticketInfo.setTheater("上海大剧院");
        ticketInfo.setPrice("￥380元");
        ticketInfo.setSeat("1层A区5排8座、1层A区5排9座、1层A区5排10座"); // 长座位信息，测试换行

        // 创建用户信息
        UserInfo userInfo = new UserInfo();
        userInfo.setAvatarUrl(
                "https://sys.jiaozuoer.cn/api/profile/upload/2023/09/14/tmp_4f8d800e088916e81facc223d00ab06e07b66a6f813f8d2e_20230914130444A003.png");
        userInfo.setName("张三");

        // 生成电子票到文件
        String outputPath = "/Users/<USER>/Documents/tmp/eticket_comprehensive_test.png";
        boolean success = ETicketCompositeUtil.generateETicket(ticketInfo, userInfo, outputPath);

        if (success) {
            logger.info("综合测试成功，输出文件：{}", outputPath);
            logger.info("请检查生成的图片中以下修复效果：");
            logger.info("1. 画布尺寸是否根据背景图片等比例计算");
            logger.info("2. 编号背景是否显示完美的半圆效果");
            logger.info("3. 编号文字是否在背景区域内垂直居中");
            logger.info("4. 演出信息是否在left: 18rpx, 宽度: 484rpx内居中显示");
            logger.info("5. 底部内容区所有元素是否都在正确位置，没有溢出");
            logger.info("6. 长座位信息是否正确换行显示");
        } else {
            logger.error("综合测试失败");
        }
    }

    /**
     * 生成字节数组示例（用于接口返回）
     */
    public static byte[] generateBytesExample() {
        // 创建票据信息
        TicketInfo ticketInfo = new TicketInfo();
        ticketInfo.setCoverFrontUrl("https://example.com/background.jpg");
        ticketInfo.setRepertoireCoverPictureUrl("https://example.com/avatar.jpg");
        ticketInfo.setRepertoire("芭蕾舞《天鹅湖》");
        ticketInfo.setSerialNumber("C111222333");
        ticketInfo.setDateTime("2025年3月20日 19:30");
        ticketInfo.setTheater("国家大剧院");
        ticketInfo.setPrice("1280元");
        ticketInfo.setSeat("池座A区12排15号");

        // 创建用户信息
        UserInfo userInfo = new UserInfo();
        userInfo.setAvatarUrl("https://example.com/user_avatar3.jpg");
        userInfo.setName("王五");

        // 生成电子票字节数组
        byte[] ticketBytes = ETicketCompositeUtil.generateETicketBytes(ticketInfo, userInfo);

        if (ticketBytes != null) {
            logger.info("电子票字节数组生成成功，大小：{} bytes", ticketBytes.length);

            // 可以将字节数组保存到文件进行验证
            try {
                String outputPath = "/tmp/eticket_bytes_example.png";
                try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                    fos.write(ticketBytes);
                }
                logger.info("字节数组已保存到文件：{}", outputPath);
            } catch (IOException e) {
                logger.error("保存字节数组到文件失败", e);
            }

            return ticketBytes;
        } else {
            logger.error("电子票字节数组生成失败");
            return null;
        }
    }

    /**
     * 异常情况处理示例
     */
    public static void exceptionHandlingExample() {
        // 创建包含无效数据的票据信息
        TicketInfo ticketInfo = new TicketInfo();
        ticketInfo.setCoverFrontUrl("invalid_url"); // 无效的图片URL
        ticketInfo.setRepertoireCoverPictureUrl("another_invalid_url"); // 无效的头像URL
        ticketInfo.setRepertoire("测试剧目");
        ticketInfo.setSerialNumber("TEST123");
        ticketInfo.setDateTime("2025年12月31日 23:59");
        ticketInfo.setTheater("测试剧院");
        ticketInfo.setPrice("免费"); // 特殊价格格式
        ticketInfo.setSeat("测试座位");

        // 创建用户信息
        UserInfo userInfo = new UserInfo();
        userInfo.setAvatarUrl("invalid_avatar_url"); // 无效的用户头像URL
        userInfo.setName("测试用户");

        // 尝试生成电子票（应该能够正常生成，只是图片会使用降级方案）
        String outputPath = "/tmp/eticket_exception_handling.png";
        boolean success = ETicketCompositeUtil.generateETicket(ticketInfo, userInfo, outputPath);

        if (success) {
            logger.info("异常处理测试电子票生成成功（使用降级方案），保存路径：{}", outputPath);
        } else {
            logger.error("异常处理测试电子票生成失败");
        }
    }

    /**
     * 主方法 - 运行所有示例
     */
    public static void main(String[] args) {
        logger.info("开始电子票合成示例演示");

        // 字节数组示例
        // logger.info("=== 字节数组生成示例 ===");
        // generateBytesExample();

        // 异常处理示例
        logger.info("=== 异常处理示例 ===");
        // exceptionHandlingExample();

        // 综合测试
        logger.info("=== 综合测试 ===");
        comprehensiveTest();

        logger.info("电子票合成示例演示完成");
    }
}