package com.youying.common.utils.eticket;

/**
 * 电子票信息数据模型
 * 包含电子票合成所需的所有票据相关信息
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class TicketInfo {

    /**
     * 背景图片URL
     */
    private String coverFrontUrl;

    /**
     * 剧目封面图片URL
     */
    private String repertoireCoverPictureUrl;

    /**
     * 剧目名称
     */
    private String repertoire;

    /**
     * 专属编号
     */
    private String serialNumber;

    /**
     * 演出时间
     */
    private String dateTime;

    /**
     * 演出地点
     */
    private String theater;

    /**
     * 票价（原始数据，需要处理）
     */
    private String price;

    /**
     * 座位信息
     */
    private String seat;

    // 构造方法
    public TicketInfo() {
    }

    public TicketInfo(Integer ticketH, String coverFrontUrl, String repertoireCoverPictureUrl,
            String repertoire, String serialNumber, String dateTime,
            String theater, String price, String seat) {
        this.coverFrontUrl = coverFrontUrl;
        this.repertoireCoverPictureUrl = repertoireCoverPictureUrl;
        this.repertoire = repertoire;
        this.serialNumber = serialNumber;
        this.dateTime = dateTime;
        this.theater = theater;
        this.price = price;
        this.seat = seat;
    }

    public String getCoverFrontUrl() {
        return coverFrontUrl;
    }

    public void setCoverFrontUrl(String coverFrontUrl) {
        this.coverFrontUrl = coverFrontUrl;
    }

    public String getRepertoireCoverPictureUrl() {
        return repertoireCoverPictureUrl;
    }

    public void setRepertoireCoverPictureUrl(String repertoireCoverPictureUrl) {
        this.repertoireCoverPictureUrl = repertoireCoverPictureUrl;
    }

    public String getRepertoire() {
        return repertoire;
    }

    public void setRepertoire(String repertoire) {
        this.repertoire = repertoire;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public String getTheater() {
        return theater;
    }

    public void setTheater(String theater) {
        this.theater = theater;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getSeat() {
        return seat;
    }

    public void setSeat(String seat) {
        this.seat = seat;
    }

    @Override
    public String toString() {
        return "TicketInfo{" +
                ", coverFrontUrl='" + coverFrontUrl + '\'' +
                ", repertoireCoverPictureUrl='" + repertoireCoverPictureUrl + '\'' +
                ", repertoire='" + repertoire + '\'' +
                ", serialNumber='" + serialNumber + '\'' +
                ", dateTime='" + dateTime + '\'' +
                ", theater='" + theater + '\'' +
                ", price='" + price + '\'' +
                ", seat='" + seat + '\'' +
                '}';
    }
}