package com.youying.common.utils.eticket;

/**
 * 用户信息数据模型
 * 包含电子票合成所需的用户相关信息
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class UserInfo {

    /**
     * 用户头像URL
     */
    private String avatarUrl;

    /**
     * 用户昵称
     */
    private String name;

    // 构造方法
    public UserInfo() {
    }

    public UserInfo(String avatarUrl, String name) {
        this.avatarUrl = avatarUrl;
        this.name = name;
    }

    // Getter 和 Setter 方法
    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "UserInfo{" +
                "avatarUrl='" + avatarUrl + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}