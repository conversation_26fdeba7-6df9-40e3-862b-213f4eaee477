package com.youying.common.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-06-28
 */
public class WechatUtil {
    private static String WECHAT_APP_ID = "wxd8b884325915e7d6";
    private static String WECHAT_SECRET = "fc00b18abfb342611bcd1eec406381e0";

    /**
     * 获取小程序二维码
     * returnUrl 微信小程序请求路径
     * parameter 请求参数，根据业务来
     */
    public static byte[] sendWechatQr(String returnUrl, Long parameter) {
        Map<String, String> map = getAccessToken();
        String url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=%s";
        // access_token 上面方法取出的凭证
        String qrUrl = String.format(url, map.get("access_token"));
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("page", returnUrl);
        paramMap.put("scene", parameter);
        paramMap.put("check_path", false);
        paramMap.put("env_version", "release");
        String param = JSON.toJSONString(paramMap);
        // hutool工具包
        return HttpRequest.post(qrUrl)
                .body(param)
                .execute()
                .bodyBytes();
    }

    /**
     * 获取微信小程序接口access_token凭证
     */
    public static Map<String, String> getAccessToken() {
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + WECHAT_APP_ID + "&secret=" + WECHAT_SECRET;
        // 这里使用的是hutool工具
        String authStr = HttpUtil.get(url);
        Map<String, String> accessToken = JSON.parseObject(authStr, Map.class);
        // accessToken包含access_token凭证和expires_in过期时间7200秒
        return accessToken;
    }
}
