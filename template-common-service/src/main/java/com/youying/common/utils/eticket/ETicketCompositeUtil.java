package com.youying.common.utils.eticket;

import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.GradientPaint;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.geom.Ellipse2D;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import javax.imageio.ImageIO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 电子票合成工具类
 * 根据用户电子票合成规范实现电子票的生成
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
public class ETicketCompositeUtil {

    private static final Logger logger = LoggerFactory.getLogger(ETicketCompositeUtil.class);

    // 画布基础配置
    private static final int CANVAS_WIDTH_RPX = 520; // 画布宽度（rpx）
    private static final int BORDER_RADIUS_RPX = 40; // 圆角半径（rpx）
    private static final double PIXEL_RATIO = 2.0; // 像素比例

    // 顶部内容区配置
    private static final int TOP_PADDING_RPX = 36; // 顶部内边距
    private static final int AVATAR_SIZE_RPX = 37; // 剧场头像尺寸
    private static final int AVATAR_MARGIN_LEFT_RPX = 10; // 头像左边距

    // 底部内容区配置
    private static final int BOTTOM_CONTENT_HEIGHT_RPX = 313; // 底部内容区高度
    private static final int USER_AVATAR_SIZE_RPX = 59; // 用户头像尺寸

    // 颜色定义
    private static final Color COLOR_WHITE = Color.WHITE;
    private static final Color COLOR_PURPLE = new Color(57, 37, 132); // #392584
    private static final Color COLOR_LIGHT_PURPLE = new Color(230, 218, 255); // #E6DAFF
    private static final Color COLOR_WHITE_60 = new Color(255, 255, 255, 153); // rgba(255,255,255,0.6)
    private static final Color COLOR_WHITE_20 = new Color(255, 255, 255, 51); // rgba(255,255,255,0.2)

    /**
     * rpx转像素的转换方法
     * 根据规范：1rpx在iPhone6上=0.5px，按2倍像素密度处理
     */
    private static int rpxToPx(int rpx) {
        return (int) (rpx * PIXEL_RATIO);
    }

    /**
     * 计算画布尺寸
     * 根据背景图片的宽高比等比例计算画布高度
     * 
     * @param coverFrontUrl  背景图片URL
     * @param fallbackHeight 备用高度（当无法获取背景图片时使用）
     * @return 画布高度（像素）
     */
    private static int calculateCanvasHeight(String coverFrontUrl, int fallbackHeight) {
        try {
            // 加载背景图片
            BufferedImage backgroundImg = loadImageFromUrl(coverFrontUrl);
            if (backgroundImg != null) {
                int imgWidth = backgroundImg.getWidth();
                int imgHeight = backgroundImg.getHeight();

                if (imgWidth > 0 && imgHeight > 0) {
                    // 计算画布宽度（像素）
                    int canvasWidth = rpxToPx(CANVAS_WIDTH_RPX);

                    // 根据背景图片的宽高比等比例计算画布高度
                    double aspectRatio = (double) imgHeight / imgWidth;
                    int calculatedHeight = (int) (canvasWidth * aspectRatio);

                    logger.info("背景图片尺寸: {}x{}, 画布宽度: {}, 计算得出画布高度: {}",
                            imgWidth, imgHeight, canvasWidth, calculatedHeight);

                    return calculatedHeight;
                }
            }
        } catch (Exception e) {
            logger.warn("无法获取背景图片尺寸，使用备用高度: {}", fallbackHeight, e);
        }

        // 降级处理：使用备用高度
        return rpxToPx(fallbackHeight);
    }

    /**
     * 生成电子票图片
     * 
     * @param ticketInfo 票据信息
     * @param userInfo   用户信息
     * @param outputPath 输出文件路径
     * @return 是否生成成功
     */
    public static boolean generateETicket(TicketInfo ticketInfo, UserInfo userInfo, String outputPath) {
        try {
            // 参数验证
            if (ticketInfo == null || userInfo == null) {
                logger.error("票据信息或用户信息不能为空");
                return false;
            }

            // 计算画布尺寸
            int canvasWidth = rpxToPx(CANVAS_WIDTH_RPX);
            int canvasHeight = calculateCanvasHeight(ticketInfo.getCoverFrontUrl(), 900);

            // 创建画布
            BufferedImage canvas = new BufferedImage(canvasWidth, canvasHeight, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = canvas.createGraphics();

            // 设置渲染质量
            setupRenderingHints(g2d);

            // 绘制背景
            drawBackground(g2d, canvasWidth, canvasHeight, ticketInfo.getCoverFrontUrl());

            // 绘制顶部内容区
            drawTopContent(g2d, canvasWidth, ticketInfo);

            // 绘制底部内容区
            drawBottomContent(g2d, canvasWidth, canvasHeight, ticketInfo, userInfo);

            // 保存图片
            g2d.dispose();
            File outputFile = new File(outputPath);
            outputFile.getParentFile().mkdirs();
            boolean success = ImageIO.write(canvas, "PNG", outputFile);

            if (success) {
                logger.info("电子票生成成功：{}", outputPath);
            } else {
                logger.error("电子票保存失败：{}", outputPath);
            }

            return success;

        } catch (Exception e) {
            logger.error("生成电子票失败", e);
            return false;
        }
    }

    /**
     * 设置渲染质量
     */
    private static void setupRenderingHints(Graphics2D g2d) {
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);
    }

    /**
     * 绘制背景
     */
    private static void drawBackground(Graphics2D g2d, int width, int height, String backgroundUrl) {
        try {
            // 创建圆角矩形
            int radius = rpxToPx(BORDER_RADIUS_RPX);
            // 注意：RoundRectangle2D.Float的arcWidth和arcHeight参数是直径，不是半径
            RoundRectangle2D roundRect = new RoundRectangle2D.Float(0, 0, width, height, radius * 2, radius * 2);

            // 设置裁剪区域
            g2d.setClip(roundRect);

            // 加载并绘制背景图片
            BufferedImage backgroundImg = loadImageFromUrl(backgroundUrl);
            if (backgroundImg != null) {
                // 缩放图片以覆盖整个画布
                Image scaledImg = backgroundImg.getScaledInstance(width, height, Image.SCALE_SMOOTH);
                g2d.drawImage(scaledImg, 0, 0, null);
            } else {
                // 降级处理：使用渐变背景
                GradientPaint gradient = new GradientPaint(0, 0, new Color(106, 90, 205),
                        0, height, new Color(72, 61, 139));
                g2d.setPaint(gradient);
                g2d.fill(roundRect);
            }

            // 重置裁剪区域
            g2d.setClip(null);

        } catch (Exception e) {
            logger.warn("绘制背景失败，使用默认背景", e);
            // 使用默认背景色
            g2d.setColor(new Color(106, 90, 205));
            int radius = rpxToPx(BORDER_RADIUS_RPX);
            // 注意：RoundRectangle2D.Float的arcWidth和arcHeight参数是直径，不是半径
            RoundRectangle2D roundRect = new RoundRectangle2D.Float(0, 0, width, height, radius * 2, radius * 2);
            g2d.fill(roundRect);
        }
    }

    /**
     * 绘制顶部内容区
     */
    private static void drawTopContent(Graphics2D g2d, int canvasWidth, TicketInfo ticketInfo) {
        int padding = rpxToPx(TOP_PADDING_RPX);
        int avatarSize = rpxToPx(AVATAR_SIZE_RPX);
        int avatarMarginLeft = rpxToPx(AVATAR_MARGIN_LEFT_RPX);

        // 绘制剧场头像
        BufferedImage avatarImg = loadImageFromUrl(ticketInfo.getRepertoireCoverPictureUrl());
        if (avatarImg != null) {
            BufferedImage circularAvatar = createCircularImage(avatarImg, avatarSize);
            g2d.drawImage(circularAvatar, padding, padding, null);
        }

        // 绘制剧目名称
        String repertoireName = (ticketInfo.getRepertoire() != null ? ticketInfo.getRepertoire() : "") + "电子票根";
        g2d.setColor(COLOR_WHITE);
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, rpxToPx(28)));
        FontMetrics fm = g2d.getFontMetrics();

        int repertoireTextX = padding + avatarSize + avatarMarginLeft;
        int maxTextWidth = canvasWidth - repertoireTextX - padding; // 可用宽度

        int repertoireBottomY; // 记录剧目名称的底部位置，用于调整专属编号位置

        if (repertoireName.length() <= 16 && fm.stringWidth(repertoireName) <= maxTextWidth) {
            // 短标题，单行显示，保持与头像水平对齐
            int textY = padding + (avatarSize + fm.getAscent()) / 2;
            g2d.drawString(repertoireName, repertoireTextX, textY);
            repertoireBottomY = textY + fm.getDescent();
        } else {
            // 长标题，需要换行显示，从头像顶部开始
            String[] lines = wrapText(repertoireName, fm, maxTextWidth);
            int lineHeight = fm.getHeight();
            int startY = padding + fm.getAscent(); // 从头像顶部开始

            for (int i = 0; i < lines.length && i < 2; i++) { // 最多显示2行
                g2d.drawString(lines[i], repertoireTextX, startY + i * lineHeight);
            }
            repertoireBottomY = startY + (Math.min(lines.length, 2) - 1) * lineHeight + fm.getDescent();
        }

        // 绘制专属编号 - 根据剧目名称是否换行调整位置
        if (ticketInfo.getSerialNumber() != null && !ticketInfo.getSerialNumber().isEmpty()) {
            String serialNumber = ticketInfo.getSerialNumber();
            g2d.setFont(new Font("微软雅黑", Font.PLAIN, rpxToPx(26)));
            FontMetrics serialFm = g2d.getFontMetrics();

            // 计算编号背景尺寸
            int textWidth = serialFm.stringWidth(serialNumber);
            int bgWidth = textWidth + rpxToPx(20); // 左右各10rpx内边距
            int bgHeight = rpxToPx(34);
            int bgRadius = rpxToPx(17);

            // 绘制编号背景 - 位置根据剧目名称底部调整
            int bgX = padding;
            int bgY = Math.max(repertoireBottomY + rpxToPx(10), padding + avatarSize + rpxToPx(10));
            g2d.setColor(COLOR_LIGHT_PURPLE);
            // 注意：RoundRectangle2D.Float的arcWidth和arcHeight参数是直径，不是半径
            // 所以要使用bgRadius * 2来形成完美的半圆效果
            RoundRectangle2D bgRect = new RoundRectangle2D.Float(bgX, bgY, bgWidth, bgHeight, bgRadius * 2,
                    bgRadius * 2);
            g2d.fill(bgRect);

            // 绘制编号文字
            g2d.setColor(COLOR_PURPLE);
            int textX = bgX + rpxToPx(10);
            // 正确的垂直居中计算：背景中心 + 字体基线偏移
            int textY2 = bgY + (bgHeight - serialFm.getHeight()) / 2 + serialFm.getAscent();
            g2d.drawString(serialNumber, textX, textY2);
        }
    }

    /**
     * 绘制底部内容区
     * 严格按照电子票合成规范实现
     */
    private static void drawBottomContent(Graphics2D g2d, int canvasWidth, int canvasHeight,
            TicketInfo ticketInfo, UserInfo userInfo) {
        int bottomHeight = rpxToPx(BOTTOM_CONTENT_HEIGHT_RPX);
        int userAvatarSize = rpxToPx(USER_AVATAR_SIZE_RPX);

        // 底部内容区的起始Y坐标
        int bottomStartY = canvasHeight - bottomHeight;

        // 绘制用户头像 - 规范：top: 0, left: 34rpx
        BufferedImage userAvatarImg = loadImageFromUrl(userInfo.getAvatarUrl());
        if (userAvatarImg != null) {
            BufferedImage circularUserAvatar = createCircularImage(userAvatarImg, userAvatarSize);
            int avatarX = rpxToPx(34);
            int avatarY = bottomStartY;
            g2d.drawImage(circularUserAvatar, avatarX, avatarY, null);
        }

        // 绘制用户昵称 - 规范：top: 8rpx, left: 106rpx, 字体大小: 32rpx
        if (userInfo.getName() != null) {
            g2d.setColor(COLOR_WHITE);
            g2d.setFont(new Font("微软雅黑", Font.PLAIN, rpxToPx(32)));
            FontMetrics nameFm = g2d.getFontMetrics();
            int nameX = rpxToPx(106);
            int nameY = bottomStartY + rpxToPx(8) + nameFm.getAscent();
            g2d.drawString(userInfo.getName(), nameX, nameY);
        }

        // 绘制演出信息 - 规范：bottom: 50rpx, left: 18rpx, 宽度: 484rpx, 字体大小: 24rpx, 居中对齐
        String showInfo = (ticketInfo.getDateTime() != null ? ticketInfo.getDateTime() : "") +
                " | " + (ticketInfo.getTheater() != null ? ticketInfo.getTheater() : "");
        g2d.setColor(COLOR_WHITE);
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, rpxToPx(24)));
        FontMetrics showFm = g2d.getFontMetrics();

        // 规范要求：left: 18rpx, 宽度: 484rpx, 居中对齐
        int showInfoLeft = rpxToPx(18);
        int showInfoWidth = rpxToPx(484);
        int showInfoX = showInfoLeft + (showInfoWidth - showFm.stringWidth(showInfo)) / 2; // 在484rpx宽度内居中
        int showInfoY = bottomStartY + bottomHeight - rpxToPx(170);
        g2d.drawString(showInfo, showInfoX, showInfoY);

        // 绘制分隔线 - 规范：bottom: 146rpx, left: 34rpx, 宽度: 446rpx, 高度: 2rpx
        g2d.setColor(COLOR_WHITE_20);
        int lineY = bottomStartY + bottomHeight - rpxToPx(146);
        g2d.fillRect(rpxToPx(34), lineY, rpxToPx(446), rpxToPx(2));

        // 绘制票价标签 - 规范：bottom: 86rpx, left: 34rpx, 字体大小: 24rpx
        g2d.setColor(COLOR_WHITE_60);
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, rpxToPx(24)));
        int labelY = bottomStartY + bottomHeight - rpxToPx(86);
        g2d.drawString("1张 | 总票价", rpxToPx(34), labelY);

        // 绘制座位标签 - 规范：bottom: 86rpx, left: 208rpx, 字体大小: 24rpx
        g2d.drawString("座位", rpxToPx(208), labelY);

        // 绘制总票价 - 规范：bottom: 22rpx, left: 34rpx
        String processedPrice = processPrice(ticketInfo.getPrice());

        // 绘制货币符号 - 规范：字体大小: 24rpx
        g2d.setColor(COLOR_WHITE);
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, rpxToPx(24)));
        int currencyY = bottomStartY + bottomHeight - rpxToPx(32);
        g2d.drawString("￥", rpxToPx(34), currencyY);

        // 绘制价格数值 - 规范：字体大小: 28rpx, 粗细: bold
        g2d.setFont(new Font("微软雅黑", Font.BOLD, rpxToPx(28)));
        int priceY = bottomStartY + bottomHeight - rpxToPx(32);
        g2d.drawString(processedPrice, rpxToPx(34) + rpxToPx(24), priceY);

        // 绘制座位信息
        if (ticketInfo.getSeat() != null && !ticketInfo.getSeat().isEmpty()) {
            String seatInfo = ticketInfo.getSeat();

            if (seatInfo.length() <= 10) {
                // 短座位信息 - 规范：bottom: 32rpx, left: 200rpx, 字体大小: 28rpx
                g2d.setColor(COLOR_WHITE);
                g2d.setFont(new Font("微软雅黑", Font.PLAIN, rpxToPx(28)));

                int shortSeatY = bottomStartY + bottomHeight - rpxToPx(32);
                g2d.drawString(seatInfo, rpxToPx(200), shortSeatY);
            } else {
                // 长座位信息 - 规范：bottom: 12rpx, left: 200rpx, 字体大小: 22rpx
                g2d.setColor(COLOR_WHITE);
                g2d.setFont(new Font("微软雅黑", Font.PLAIN, rpxToPx(22)));
                FontMetrics seatFm = g2d.getFontMetrics();

                // 简单换行处理
                int maxWidth = rpxToPx(300);
                String[] lines = wrapText(seatInfo, seatFm, maxWidth);
                // 长座位信息需要更多空间，所以起始Y坐标要更小（更靠上）
                // 规范：最小高度68rpx，行高34rpx，所以第二行的bottom应该是12rpx + 34rpx = 46rpx
                int startY = bottomStartY + bottomHeight - rpxToPx(46);

                for (int i = 0; i < lines.length && i < 2; i++) {
                    g2d.drawString(lines[i], rpxToPx(200), startY + i * rpxToPx(34));
                }
            }
        }
    }

    /**
     * 处理价格数据
     * 根据规范移除"元"、"￥"、"¥"字符，并进行数值转换
     */
    private static String processPrice(String price) {
        if (price == null || price.isEmpty()) {
            return "0";
        }

        String temp = price;
        temp = temp.replace("元", "");
        temp = temp.replace("￥", "");
        temp = temp.replace("¥", "");

        try {
            double numPrice = Double.parseDouble(temp);
            if (numPrice == 0) {
                return "0";
            }
            // 如果是整数，去掉小数点
            if (numPrice == (long) numPrice) {
                return String.valueOf((long) numPrice);
            } else {
                return String.valueOf(numPrice);
            }
        } catch (NumberFormatException e) {
            return temp;
        }
    }

    /**
     * 从URL加载图片
     */
    private static BufferedImage loadImageFromUrl(String imageUrl) {
        if (imageUrl == null || imageUrl.isEmpty()) {
            return null;
        }

        try {
            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);

            try (InputStream inputStream = connection.getInputStream()) {
                return ImageIO.read(inputStream);
            }
        } catch (Exception e) {
            logger.warn("加载图片失败: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * 创建圆形图片
     */
    private static BufferedImage createCircularImage(BufferedImage source, int size) {
        BufferedImage output = new BufferedImage(size, size, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = output.createGraphics();

        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 创建圆形裁剪区域
        Ellipse2D circle = new Ellipse2D.Float(0, 0, size, size);
        g2d.setClip(circle);

        // 绘制缩放后的图片
        Image scaledImg = source.getScaledInstance(size, size, Image.SCALE_SMOOTH);
        g2d.drawImage(scaledImg, 0, 0, null);

        g2d.dispose();
        return output;
    }

    /**
     * 文本换行处理 - 贪心算法，简单有效
     */
    private static String[] wrapText(String text, FontMetrics fm, int maxWidth) {
        if (fm.stringWidth(text) <= maxWidth) {
            return new String[] { text };
        }

        // 贪心策略：从左到右添加字符，直到第一行达到合适长度
        int splitIndex = 1;

        // 第一步：找到第一行不超过maxWidth的最大长度
        for (int i = 1; i <= text.length(); i++) {
            String line1 = text.substring(0, i);
            if (fm.stringWidth(line1) > maxWidth) {
                splitIndex = i - 1; // 回退一个字符
                break;
            }
            splitIndex = i;
        }

        // 第二步：确保第二行也不超过maxWidth
        while (splitIndex > 1) {
            String line2 = text.substring(splitIndex);
            if (fm.stringWidth(line2) <= maxWidth) {
                break; // 找到了合适的分割点
            }
            splitIndex--; // 第二行太长，继续回退
        }

        // 第三步：在合理范围内微调，寻找更好的分割点（如标点符号）
        int finalSplitIndex = splitIndex;
        int searchRange = Math.min(3, splitIndex / 4); // 在前后3个字符或25%范围内搜索

        for (int offset = -searchRange; offset <= searchRange; offset++) {
            int testIndex = splitIndex + offset;
            if (testIndex < 1 || testIndex >= text.length())
                continue;

            char charAtSplit = text.charAt(testIndex - 1);
            String line1 = text.substring(0, testIndex);
            String line2 = text.substring(testIndex);

            // 检查宽度约束
            if (fm.stringWidth(line1) > maxWidth || fm.stringWidth(line2) > maxWidth) {
                continue;
            }

            // 优先在标点符号处分割
            if (charAtSplit == '、' || charAtSplit == '，' || charAtSplit == '。' ||
                    charAtSplit == '！' || charAtSplit == '？' || charAtSplit == '；' ||
                    charAtSplit == '》' || charAtSplit == '』' || charAtSplit == '）') {
                finalSplitIndex = testIndex;
                break;
            }
        }

        String line1 = text.substring(0, finalSplitIndex);
        String line2 = text.substring(finalSplitIndex);

        logger.info("贪心算法分割：'{}' | '{}'，宽度：{} | {}",
                line1, line2, fm.stringWidth(line1), fm.stringWidth(line2));

        return new String[] { line1, line2 };
    }

    /**
     * 生成电子票并返回字节数组
     * 
     * @param ticketInfo 票据信息
     * @param userInfo   用户信息
     * @return 图片字节数组，失败返回null
     */
    public static byte[] generateETicketBytes(TicketInfo ticketInfo, UserInfo userInfo) {
        try {
            // 参数验证
            if (ticketInfo == null || userInfo == null) {
                logger.error("票据信息或用户信息不能为空");
                return null;
            }

            // 计算画布尺寸
            int canvasWidth = rpxToPx(CANVAS_WIDTH_RPX);
            int canvasHeight = calculateCanvasHeight(ticketInfo.getCoverFrontUrl(), 463);

            // 创建画布
            BufferedImage canvas = new BufferedImage(canvasWidth, canvasHeight, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = canvas.createGraphics();

            // 设置渲染质量
            setupRenderingHints(g2d);

            // 绘制背景
            drawBackground(g2d, canvasWidth, canvasHeight, ticketInfo.getCoverFrontUrl());

            // 绘制顶部内容区
            drawTopContent(g2d, canvasWidth, ticketInfo);

            // 绘制底部内容区
            drawBottomContent(g2d, canvasWidth, canvasHeight, ticketInfo, userInfo);

            // 转换为字节数组
            g2d.dispose();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            boolean success = ImageIO.write(canvas, "PNG", baos);

            if (success) {
                return baos.toByteArray();
            } else {
                logger.error("电子票转换为字节数组失败");
                return null;
            }

        } catch (Exception e) {
            logger.error("生成电子票字节数组失败", e);
            return null;
        }
    }
}