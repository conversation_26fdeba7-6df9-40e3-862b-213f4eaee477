package com.youying.common.constant;

/**
 * <AUTHOR>
 * @Date 2023/5/11
 */
public class BlockchainConstants {
    /**
     * 请求路径
     */
//    public static final String SERVER_URL = "https://linyi.test.bestpay.net/gw/api";

    /**
     * 请求路径
     */
    public static final String SERVER_URL = "https://linyi.bestpay.com.cn/gw/api";

    /**
     * 创建藏品
     */
    public static final String CREATE_SKU_NAME = "OpenSkuCreate";

    /**
     * 创建藏品
     */
    public static final String OPEN_SKU_QUERY = "OpenSkuQuery";

    /**
     * 发放数字藏品
     */
    public static final String USER_SEND_NAME = "UserSend";

    /**
     * 查询藏品发放结果
     */
    public static final String QUERY_SEND_RESULT_NAME = "QuerySendResult";

    /**
     * 查看用户账户下的某个藏品的详情信息
     */
    public static final String QUERY_USER_SKU_NAME = "QueryUserSku";

    /**
     * 查询用户账户下的藏品列表
     */
    public static final String QUERY_USER_SKU_LIST_NAME = "QueryUserSkuList";

    /**
     * 用户生成场景页面的授权访问链接
     */
    public static final String GET_OPEN_ACCESS_URL_NAME = "OpenAccessUrl";
}
