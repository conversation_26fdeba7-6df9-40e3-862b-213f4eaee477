package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 免责声明表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-25
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_portfolio_statement")
public class PortfolioStatement extends Model<PortfolioStatement> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类型（1、普通，2、升级）
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 免责声明
     */
    @TableField("statement")
    private String statement;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
