package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 商家员工表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_merchant_user")
public class MerchantUser extends Model<MerchantUser> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    @TableField(value = "merchant_id", fill = FieldFill.INSERT)
    private Long merchantId;

    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;

    /**
     * 商家类别（1剧目，2剧场）
     */
    @TableField("merchant_category")
    private Integer merchantCategory;

    /**
     * 超级管理员(su)
     */
    @TableField("`type`")
    private String type;

    /**
     * 名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 头像
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 性别
     */
    @TableField("sex")
    private Integer sex;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 密码
     */
    @TableField("`password`")
    private String password;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 是否删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
