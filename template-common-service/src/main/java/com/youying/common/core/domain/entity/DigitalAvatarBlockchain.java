package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 电子头像区块链表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_digital_avatar_blockchain")
public class DigitalAvatarBlockchain extends Model<DigitalAvatarBlockchain> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数字头像ID
     */
    @TableField("digital_avatar_id")
    private Long digitalAvatarId;

    /**
     * 商品ID
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * 空投ID
     */
    @TableField("drop_id")
    private String dropId;

    /**
     * 商家ID
     */
    @TableField(value = "merchant_id", fill = FieldFill.INSERT)
    private Long merchantId;

    /**
     * 叠加处理图片
     */
    @TableField("overlay_image")
    private String overlayImage;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    @TableField("user_receiving_records_id")
    private Long userReceivingRecordsId;

    @TableField("user_receiving_records_no")
    private String userReceivingRecordsNo;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
