package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_image_scan_record")
public class ImageScanRecord extends Model<ImageScanRecord> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 手机型号
     */
    @TableField("device_model")
    private String deviceModel;

    /**
     * 扫码商品组合ID
     */
    @TableField("portfolio_id")
    private Long portfolioId;

    /**
     * 扫描图片路径
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 扫描结果
     */
    @TableField("body")
    private String body;

    /**
     * 详情结果
     */
    @TableField("word_list")
    private String wordList;

    /**
     * 扫描时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
