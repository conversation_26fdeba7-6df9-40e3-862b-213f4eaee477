package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 等级勋章详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_rank_medal_info")
public class RankMedalInfo extends Model<RankMedalInfo> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    @TableField(value = "merchant_id", fill = FieldFill.INSERT)
    private Long merchantId;

    /**
     * 等级勋章ID
     */
    @TableField("rank_medal_id")
    private Long rankMedalId;

    /**
     * 等级勋章名称
     */
    @TableField("rank_medal_name")
    private String rankMedalName;

    /**
     * 勋章等级
     */
    @TableField("`name`")
    private String name;

    /**
     * 消费金额
     */
    @TableField("expense_price")
    private BigDecimal expensePrice;

    /**
     * 消费次数
     */
    @TableField("expense_number")
    private Integer expenseNumber;

    /**
     * 颜色
     */
    @TableField("color")
    private String color;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
