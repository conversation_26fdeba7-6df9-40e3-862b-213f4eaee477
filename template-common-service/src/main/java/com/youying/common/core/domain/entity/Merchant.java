package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 商家表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_merchant")
public class Merchant extends Model<Merchant> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家名称
     */
    @TableField("merchant_name")
    private String merchantName;

    /**
     * 商家类别（1剧目，2剧场）
     */
    @TableField("merchant_category")
    private Integer merchantCategory;

    /**
     * 联系人
     */
    @TableField("contact_person")
    private String contactPerson;

    /**
     * LOGO
     */
    @TableField("logo")
    private String logo;

    /**
     * 联系电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 超级管理员账号ID
     */
    @TableField("merchant_user_id")
    private Long merchantUserId;

    /**
     * 账号
     */
    @TableField("`account`")
    private String account;

    /**
     * 密码
     */
    @TableField("`password`")
    private String password;

    /**
     * 联系地址
     */
    @TableField("address")
    private String address;

    /**
     * 合作开始时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 合作结束时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 营业执照正面
     */
    @TableField("business_license_front")
    private String businessLicenseFront;

    /**
     * 营业执照反面
     */
    @TableField("business_license_reverse")
    private String businessLicenseReverse;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
