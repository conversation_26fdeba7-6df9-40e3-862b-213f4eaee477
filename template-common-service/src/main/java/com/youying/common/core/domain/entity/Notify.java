package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 通知表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_notify")
public class Notify extends Model<Notify> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 内容
     */
    @TableField("body")
    private String body;

    /**
     * 端口（1商家、2用户）
     */
    @TableField("`port`")
    private Integer port;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 推送类型（1立即推送、2手动发布、3定时推送）
     */
    @TableField("push_type")
    private Integer pushType;

    /**
     * 推送完成时间
     */
    @TableField("push_pass_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pushPassTime;

    /**
     * 定时推送时间
     */
    @TableField("timed_release_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timedReleaseTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
