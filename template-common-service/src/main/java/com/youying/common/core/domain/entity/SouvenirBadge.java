package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 剧场纪念徽章表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_souvenir_badge")
public class SouvenirBadge extends Model<SouvenirBadge> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    @TableField(value = "merchant_id", fill = FieldFill.INSERT)
    private Long merchantId;

    /**
     * 藏品名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 剧场ID
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 徽章模型，支持上传3D模型，大小100M以内
     */
    @TableField("model")
    private String model;

    /**
     * 封面图URL
     */
    @TableField("cover_picture")
    private String coverPicture;


    /**
     * 批次
     */
    @TableField("batch")
    private Integer batch;

    /**
     * 发行方
     */
    @TableField("issuer_name")
    private String issuerName;

    /**
     * 发放数量
     */
    @TableField("issued_quantity")
    private Integer issuedQuantity;

    /**
     * 发放时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 藏品简介
     */
    @TableField("introduction")
    private String introduction;

    /**
     * 短信通知
     */
    @TableField("sms_notify")
    private Integer smsNotify;

    /**
     * 橱窗展示状态
     */
    @TableField("`look_status`")
    private Integer lookStatus;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    @TableField("audit")
    private Integer audit;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    @TableField("audit_flag")
    private Integer auditFlag;

    /**
     * 审核通过时间
     */
    @TableField("audit_pass_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditPassTime;

    /**
     * 驳回原因
     */
    @TableField("reasons_rejection")
    private String reasonsRejection;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
