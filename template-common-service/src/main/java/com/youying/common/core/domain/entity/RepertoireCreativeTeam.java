package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 剧目主创团队表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_repertoire_creative_team")
public class RepertoireCreativeTeam extends Model<RepertoireCreativeTeam> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    @TableField(value = "merchant_id", fill = FieldFill.INSERT)
    private Long merchantId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 主创照片
     */
    @TableField("picture")
    private String picture;

    /**
     * 主创姓名
     */
    @TableField("`name`")
    private String name;

    /**
     * 职能名称
     */
    @TableField("function_name")
    private String functionName;

    /**
     * 简介（400）
     */
    @TableField("introduction")
    private String introduction;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
