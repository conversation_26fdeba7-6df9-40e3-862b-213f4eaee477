package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 剧目剧场点赞表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_kudos")
public class Kudos extends Model<Kudos> {

    /**
     * 剧场ID (0为暂无)
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID (0为暂无)
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 赞或踩（1赞，0踩）
     */
    @TableField("`type`")
    private Integer type;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;


    @Override
    public Serializable pkVal() {
        return null;
    }

}
