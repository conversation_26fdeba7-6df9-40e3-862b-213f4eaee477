package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 小程序设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-25
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_wechat_setting")
public class WechatSetting extends Model<WechatSetting> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 小程序LOGO
     */
    @TableField("wechat_logo")
    private String wechatLogo;

    /**
     * 小程序名称
     */
    @TableField("wechat_name")
    private String wechatName;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
