package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 剧目表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_repertoire")
public class Repertoire extends Model<Repertoire> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 剧目名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 短标题
     */
    @TableField("`short_name`")
    private String shortName;

    /**
     * 剧目类型ID
     */
    @TableField("repertoire_type_id")
    private Long repertoireTypeId;

    /**
     * 封面图URL
     */
    @TableField("cover_picture")
    private String coverPicture;

    /**
     * 剧目图片（8张）,拼接
     */
    @TableField("pictures")
    private String pictures;

    /**
     * 简介（500字）
     */
    @TableField("introduction")
    private String introduction;

    /**
     * 评分
     */
    @TableField("rating")
    private Double rating;

    /**
     * 推荐（0否，1是）
     */
    @TableField("recommend")
    private Integer recommend;

    /**
     * 推荐时间
     */
    @TableField("recommend_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recommendTime;

    /**
     * 好评率
     */
    @TableField("good_rating_rate")
    private Double goodRatingRate;

    /**
     * 二维码
     */
    @TableField("qr_code")
    private String qrCode;

    /**
     * 关注数量
     */
    @TableField("focus_number")
    private Integer focusNumber;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    @TableField("audit")
    private Integer audit;

    /**
     * 审核（0待审核，1驳回，2通过）
     */
    @TableField("audit_flag")
    private Integer auditFlag;

    /**
     * 审核通过时间
     */
    @TableField("audit_pass_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditPassTime;

    /**
     * 驳回原因
     */
    @TableField("reasons_rejection")
    private String reasonsRejection;

    /**
     * 商家ID
     */
    @TableField(value = "merchant_id", fill = FieldFill.INSERT)
    private Long merchantId;

    /**
     * 是否删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 状态
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
