package com.youying.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 用户订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("t_user_order")
public class UserOrder extends Model<UserOrder> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    @TableField("merchant_id")
    private Long merchantId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 交易编号
     */
    @TableField("transaction_id")
    private String transactionId;

    /**
     * 支付编号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 预支付交易会话标识
     */
    @TableField("prepay_id")
    private String prepayId;

    /**
     * 支付金额
     */
    @TableField("pay_price")
    private BigDecimal payPrice;

    /**
     * 退款金额
     */
    @TableField("refund_price")
    private BigDecimal refundPrice;

    /**
     * 支付状态（0待支付，1已支付）
     */
    @TableField("pay_status")
    private Integer payStatus;

    /**
     * 退款状态（0未退款，1已退款）
     */
    @TableField("refund_status")
    private Integer refundStatus;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 退款时间
     */
    @TableField("refund_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;

    /**
     * 用户数字藏品领取记录ID
     */
    @TableField("user_receiving_records_id")
    private Long userReceivingRecordsId;

    /**
     * 剧场ID
     */
    @TableField("theater_id")
    private Long theaterId;

    /**
     * 剧目ID
     */
    @TableField("repertoire_id")
    private Long repertoireId;

    /**
     * 1：电子票、2：数字头像、3：纪念徽章、4：等级勋章 ID
     */
    @TableField("relation_id")
    private Long relationId;

    /**
     * 徽章类型（1：电子票、2：数字头像、3：纪念徽章、4：等级勋章）
     */
    @TableField("badge_type")
    private Integer badgeType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
